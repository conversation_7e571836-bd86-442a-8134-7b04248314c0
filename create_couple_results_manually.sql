-- Manually create couple results for testing
-- Run this in Supabase SQL Editor

-- First, let's check if couple_results policies are working
SELECT 'Checking couple_results policies...' as status;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'couple_results'
ORDER BY cmd, policyname;

-- Check if RLS is enabled
SELECT 'Checking RLS status...' as status;
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'couple_results';

-- Test INSERT into couple_results manually
-- This will help us see if there are any policy issues
SELECT 'Testing manual couple_results creation...' as status;

-- Create sample couple results data
INSERT INTO couple_results (
  couple_id,
  compatibility_scores,
  overall_compatibility,
  alignment_areas,
  conflict_areas,
  created_at,
  updated_at
) VALUES (
  '173f729b-8e62-4882-ad4b-a87f9909b4bb',
  '{
    "Visi Hidup": 75,
    "<PERSON><PERSON>an": 85,
    "<PERSON><PERSON><PERSON><PERSON>": 90,
    "<PERSON>mu<PERSON><PERSON>": 65,
    "<PERSON><PERSON><PERSON> dan <PERSON>": 70,
    "Keintiman Seksual": 60,
    "Spiritualitas": 80,
    "Sisi Gelap": 75
  }'::jsonb,
  75,
  '["Shared values in parenting", "Similar financial goals", "Strong spiritual alignment"]'::jsonb,
  '["Communication styles differ", "Different views on intimacy", "Role expectations need discussion"]'::jsonb,
  NOW(),
  NOW()
);

-- Verify the insertion
SELECT 'Verifying couple_results creation...' as status;
SELECT 
  id,
  couple_id,
  overall_compatibility,
  created_at
FROM couple_results
WHERE couple_id = '173f729b-8e62-4882-ad4b-a87f9909b4bb';

-- Test if the couple_results can be accessed by both users
-- This simulates what the application would do
SELECT 'Testing access by user 1...' as status;
-- This would normally be filtered by RLS, but we're testing manually

SELECT 'Testing access by user 2...' as status;
-- This would normally be filtered by RLS, but we're testing manually

SELECT 'Manual couple_results creation complete!' as status;
