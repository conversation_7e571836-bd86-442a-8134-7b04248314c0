-- Test RLS policies for couple_results and individual_results
-- Run this in Supabase SQL Editor

-- Set the current user context to test RLS
-- Replace with actual user ID for testing
SET LOCAL "request.jwt.claims" = '{"sub": "3dccb116-689f-4d35-86ee-096970fe9480"}';

-- Test 1: Check if user can see their individual results
SELECT 'Testing individual_results access for user 3dccb116-689f-4d35-86ee-096970fe9480...' as test;
SELECT 
  user_id,
  overall_score,
  jsonb_array_length(domains) as domain_count
FROM individual_results
WHERE user_id = '3dccb116-689f-4d35-86ee-096970fe9480';

-- Test 2: Check if user can see partner's individual results
SELECT 'Testing individual_results access for partner 71da82ac-2dcc-411f-b684-bf20a512cddf...' as test;
SELECT 
  user_id,
  overall_score,
  jsonb_array_length(domains) as domain_count
FROM individual_results
WHERE user_id = '71da82ac-2dcc-411f-b684-bf20a512cddf';

-- Test 3: Check if user can see couple data
SELECT 'Testing couples table access...' as test;
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
WHERE user_id_1 = '3dccb116-689f-4d35-86ee-096970fe9480' 
   OR user_id_2 = '3dccb116-689f-4d35-86ee-096970fe9480';

-- Test 4: Check if user can see both individual results using IN clause
SELECT 'Testing individual_results with IN clause...' as test;
SELECT 
  user_id,
  overall_score,
  jsonb_array_length(domains) as domain_count
FROM individual_results
WHERE user_id IN ('3dccb116-689f-4d35-86ee-096970fe9480', '71da82ac-2dcc-411f-b684-bf20a512cddf');

-- Reset user context and test with the other user
SET LOCAL "request.jwt.claims" = '{"sub": "71da82ac-2dcc-411f-b684-bf20a512cddf"}';

-- Test 5: Check access from the other user's perspective
SELECT 'Testing from partner perspective - individual_results with IN clause...' as test;
SELECT 
  user_id,
  overall_score,
  jsonb_array_length(domains) as domain_count
FROM individual_results
WHERE user_id IN ('3dccb116-689f-4d35-86ee-096970fe9480', '71da82ac-2dcc-411f-b684-bf20a512cddf');

-- Reset to no user context
RESET "request.jwt.claims";

-- Test 6: Check without RLS context (admin view)
SELECT 'Testing without RLS context (admin view)...' as test;
SELECT 
  user_id,
  overall_score,
  jsonb_array_length(domains) as domain_count
FROM individual_results
WHERE user_id IN ('3dccb116-689f-4d35-86ee-096970fe9480', '71da82ac-2dcc-411f-b684-bf20a512cddf');

-- Test 7: Check current RLS policies
SELECT 'Current individual_results policies:' as info;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive,
  qual
FROM pg_policies 
WHERE tablename = 'individual_results'
ORDER BY cmd, policyname;
