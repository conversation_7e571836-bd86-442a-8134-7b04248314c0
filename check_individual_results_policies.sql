-- Check RLS policies for individual_results table
-- Run this in Supabase SQL Editor

-- 1. Check if individual_results table exists and has RLS enabled
SELECT 'Checking individual_results table...' as info;
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE tablename = 'individual_results';

-- 2. Check all policies for individual_results
SELECT 'Current individual_results policies:' as info;
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    cmd,
    permissive,
    qual as condition
FROM pg_policies 
WHERE tablename = 'individual_results'
ORDER BY cmd, policyname;

-- 3. Check if we need to add missing policies
SELECT 'Checking for missing policies...' as info;

-- Add missing INSERT policy for individual_results if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'individual_results' 
        AND policyname = 'Users can insert their own individual results'
        AND cmd = 'INSERT'
    ) THEN
        CREATE POLICY "Users can insert their own individual results"
          ON individual_results FOR INSERT
          WITH CHECK (auth.uid() = user_id);
        RAISE NOTICE 'Added INSERT policy for individual_results';
    ELSE
        RAISE NOTICE 'INSERT policy already exists for individual_results';
    END IF;
END $$;

-- Add missing UPDATE policy for individual_results if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'individual_results' 
        AND policyname = 'Users can update their own individual results'
        AND cmd = 'UPDATE'
    ) THEN
        CREATE POLICY "Users can update their own individual results"
          ON individual_results FOR UPDATE
          USING (auth.uid() = user_id);
        RAISE NOTICE 'Added UPDATE policy for individual_results';
    ELSE
        RAISE NOTICE 'UPDATE policy already exists for individual_results';
    END IF;
END $$;

-- 4. Final verification
SELECT 'Final verification - individual_results policies:' as info;
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    cmd,
    permissive
FROM pg_policies 
WHERE tablename = 'individual_results'
ORDER BY cmd, policyname;
