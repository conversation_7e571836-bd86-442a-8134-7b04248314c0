-- Debug script to check couple and individual results data
-- Run this in Supabase SQL Editor or via CLI

-- 1. Check all users in auth.users
SELECT 'All users in auth.users:' as info;
SELECT 
  id,
  email,
  created_at
FROM auth.users
ORDER BY created_at DESC;

-- 2. Check all profiles
SELECT 'All profiles:' as info;
SELECT 
  id,
  full_name,
  email,
  created_at
FROM profiles
ORDER BY created_at DESC;

-- 3. Check all couples
SELECT 'All couples:' as info;
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at,
  (SELECT email FROM auth.users WHERE id = couples.user_id_1) as user1_email,
  (SELECT email FROM auth.users WHERE id = couples.user_id_2) as user2_email
FROM couples
ORDER BY created_at DESC;

-- 4. Check all individual results
SELECT 'All individual results:' as info;
SELECT 
  user_id,
  (SELECT email FROM auth.users WHERE id = individual_results.user_id) as user_email,
  overall_score,
  array_length(domains, 1) as domain_count,
  created_at,
  updated_at
FROM individual_results
ORDER BY updated_at DESC;

-- 5. Check individual results details
SELECT 'Individual results details:' as info;
SELECT 
  ir.user_id,
  (SELECT email FROM auth.users WHERE id = ir.user_id) as user_email,
  jsonb_array_length(ir.domains) as domain_count,
  jsonb_array_elements(ir.domains)->>'domain' as domain_name,
  (jsonb_array_elements(ir.domains)->>'score')::int as domain_score
FROM individual_results ir
ORDER BY ir.user_id, (jsonb_array_elements(ir.domains)->>'domain');

-- 6. Check couple invitation codes
SELECT 'Couple invitation codes:' as info;
SELECT 
  code,
  creator_user_id,
  (SELECT email FROM auth.users WHERE id = couple_invitation_codes.creator_user_id) as creator_email,
  used_by_user_id,
  (SELECT email FROM auth.users WHERE id = couple_invitation_codes.used_by_user_id) as used_by_email,
  is_active,
  used_at,
  created_at
FROM couple_invitation_codes
ORDER BY created_at DESC;

-- 7. Check couple results
SELECT 'Couple results:' as info;
SELECT 
  id,
  couple_id,
  overall_compatibility,
  created_at,
  updated_at
FROM couple_results
ORDER BY created_at DESC;
