-- Check couple connection status for the two users
-- Run this in Supabase SQL Editor

-- User IDs from individual_results:
-- User 1: 71da82ac-2dcc-411f-b684-bf20a512cddf
-- User 2: 3dccb116-689f-4d35-86ee-096970fe9480

-- 1. Check if these users exist in profiles
SELECT 'Checking profiles for both users...' as status;
SELECT 
  id,
  full_name,
  email,
  created_at
FROM profiles
WHERE id IN ('71da82ac-2dcc-411f-b684-bf20a512cddf', '3dccb116-689f-4d35-86ee-096970fe9480')
ORDER BY created_at;

-- 2. Check if they are connected as a couple
SELECT 'Checking couple connection...' as status;
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
WHERE (user_id_1 = '71da82ac-2dcc-411f-b684-bf20a512cddf' AND user_id_2 = '3dccb116-689f-4d35-86ee-096970fe9480')
   OR (user_id_1 = '3dccb116-689f-4d35-86ee-096970fe9480' AND user_id_2 = '71da82ac-2dcc-411f-b684-bf20a512cddf');

-- 3. Check invitation codes created by either user
SELECT 'Checking invitation codes...' as status;
SELECT 
  id,
  code,
  creator_user_id,
  used_by_user_id,
  is_active,
  expires_at,
  used_at,
  created_at
FROM couple_invitation_codes
WHERE creator_user_id IN ('71da82ac-2dcc-411f-b684-bf20a512cddf', '3dccb116-689f-4d35-86ee-096970fe9480')
   OR used_by_user_id IN ('71da82ac-2dcc-411f-b684-bf20a512cddf', '3dccb116-689f-4d35-86ee-096970fe9480')
ORDER BY created_at DESC;

-- 4. Check all couples (to see if there are any couples at all)
SELECT 'All couples in database...' as status;
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
ORDER BY created_at DESC;

-- 5. Check all invitation codes (to see if there are any codes at all)
SELECT 'All invitation codes in database...' as status;
SELECT 
  id,
  code,
  creator_user_id,
  used_by_user_id,
  is_active,
  expires_at,
  used_at,
  created_at
FROM couple_invitation_codes
ORDER BY created_at DESC;

-- 6. If no couple connection exists, create one manually for testing
-- UNCOMMENT THE LINES BELOW TO CREATE A COUPLE CONNECTION:

-- INSERT INTO couples (couple_id, user_id_1, user_id_2, created_at)
-- VALUES (
--   uuid_generate_v4(),
--   '71da82ac-2dcc-411f-b684-bf20a512cddf',
--   '3dccb116-689f-4d35-86ee-096970fe9480',
--   NOW()
-- );

-- SELECT 'Couple connection created manually!' as status;
