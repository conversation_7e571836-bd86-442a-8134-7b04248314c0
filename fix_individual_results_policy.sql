-- Fix individual_results RLS policy to allow partner access
-- Run this in Supabase SQL Editor

-- Check current policies
SELECT 'Current individual_results policies:' as status;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'individual_results'
ORDER BY cmd, policyname;

-- Drop existing restrictive policy
DROP POLICY IF EXISTS "individual_results_select_policy" ON individual_results;

-- Create new policy that allows users to see their own results AND their partner's results
CREATE POLICY "individual_results_select_policy"
  ON individual_results FOR SELECT
  USING (
    auth.uid() = user_id 
    OR EXISTS (
      SELECT 1 FROM couples 
      WHERE (user_id_1 = auth.uid() AND user_id_2 = individual_results.user_id)
         OR (user_id_2 = auth.uid() AND user_id_1 = individual_results.user_id)
    )
  );

-- Verify the new policy
SELECT 'Updated individual_results policies:' as status;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'individual_results'
ORDER BY cmd, policyname;

-- Test the policy with our specific users
-- Set user context to test
SET LOCAL "request.jwt.claims" = '{"sub": "3dccb116-689f-4d35-86ee-096970fe9480"}';

-- This should now return both individual results
SELECT 'Testing new policy - should return both results:' as test;
SELECT 
  user_id,
  overall_score,
  jsonb_array_length(domains) as domain_count,
  CASE 
    WHEN user_id = '3dccb116-689f-4d35-86ee-096970fe9480' THEN 'Current User'
    WHEN user_id = '71da82ac-2dcc-411f-b684-bf20a512cddf' THEN 'Partner'
    ELSE 'Other'
  END as user_type
FROM individual_results
WHERE user_id IN ('3dccb116-689f-4d35-86ee-096970fe9480', '71da82ac-2dcc-411f-b684-bf20a512cddf')
ORDER BY user_type;

-- Reset user context
RESET "request.jwt.claims";

SELECT 'Individual results policy fix complete!' as status;
