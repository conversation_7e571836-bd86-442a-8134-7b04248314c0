"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Download, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import EnhancedResultsVisualization from "@/components/assessment/EnhancedResultsVisualization";
import {
  processIndividualAssessment,
  processCoupleAssessment,
  parseResponsesFromDatabase,
  parseDomainName,
  AssessmentResponse,
  CoupleAnalysisReport,
} from "@/lib/assessment";

export default function CoupleResultsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analysisReport, setAnalysisReport] = useState<CoupleAnalysisReport | null>(null);
  const [coupleInfo, setCoupleInfo] = useState<{
    partner1Name: string;
    partner2Name: string;
  } | null>(null);

  useEffect(() => {
    loadCoupleResults();
  }, []);

  const loadCoupleResults = async () => {
    try {
      setLoading(true);
      setError(null);

      const supabase = createClient();

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) {
        throw new Error("User not authenticated");
      }

      // Get couple information
      console.log("Current user ID:", user.id);

      const { data: couple, error: coupleError } = await supabase
        .from("couples")
        .select(`
          couple_id,
          user_id_1,
          user_id_2
        `)
        .or(`user_id_1.eq.${user.id},user_id_2.eq.${user.id}`)
        .single();

      console.log("Couple query result:", couple);
      console.log("Couple query error:", coupleError);

      if (coupleError || !couple) {
        throw new Error("Couple not found. Please connect with your partner first.");
      }

      // Get profiles for both partners
      const { data: profiles, error: profilesError } = await supabase
        .from("profiles")
        .select("id, full_name, email")
        .in("id", [couple.user_id_1, couple.user_id_2]);

      if (profilesError) {
        console.error("Error loading profiles:", profilesError);
      }

      // Get individual results for both partners
      console.log("Looking for individual results for users:", [couple.user_id_1, couple.user_id_2]);

      const { data: individualResults, error: resultsError } = await supabase
        .from("individual_results")
        .select("*")
        .in("user_id", [couple.user_id_1, couple.user_id_2]);

      console.log("Individual results found:", individualResults);
      console.log("Results error:", resultsError);

      if (resultsError) {
        console.error("Error loading individual results:", resultsError);
        throw new Error("Failed to load assessment results");
      }

      if (!individualResults || individualResults.length === 0) {
        console.error("No individual results found for users:", [couple.user_id_1, couple.user_id_2]);
        throw new Error("No assessment results found. Please complete your assessments first.");
      }

      console.log(`Found ${individualResults.length} individual results out of 2 needed`);

      if (individualResults.length < 2) {
        // Let's check what we actually have
        const user1HasResults = individualResults.some(r => r.user_id === couple.user_id_1);
        const user2HasResults = individualResults.some(r => r.user_id === couple.user_id_2);

        console.log("User 1 has results:", user1HasResults);
        console.log("User 2 has results:", user2HasResults);
        console.log("User 1 ID:", couple.user_id_1);
        console.log("User 2 ID:", couple.user_id_2);
        console.log("Individual results user IDs:", individualResults.map(r => r.user_id));

        throw new Error("Both partners need to complete their assessments first to view couple compatibility results");
      }

      console.log("✅ Both partners have completed assessments, proceeding with couple analysis...");

      // Find results for each partner
      const partner1Results = individualResults.find(r => r.user_id === couple.user_id_1);
      const partner2Results = individualResults.find(r => r.user_id === couple.user_id_2);

      if (!partner1Results || !partner2Results) {
        throw new Error("Assessment results incomplete for one or both partners");
      }

      // Convert database format to assessment responses
      const partner1Responses: AssessmentResponse[] = [];
      const partner2Responses: AssessmentResponse[] = [];

      // Extract responses from domains
      console.log("Converting partner 1 domains...");
      (partner1Results.domains as any[]).forEach((domain: any) => {
        if (domain.responses) {
          const originalDomain = domain.domain;
          const convertedDomain = parseDomainName(domain.domain);
          console.log(`Domain conversion: "${originalDomain}" -> "${convertedDomain}"`);

          Object.entries(domain.responses).forEach(([questionId, answer]) => {
            partner1Responses.push({
              questionId,
              answer: answer as string,
              domain: convertedDomain // Convert formatted name to domain ID
            });
          });
        }
      });

      console.log("Converting partner 2 domains...");
      (partner2Results.domains as any[]).forEach((domain: any) => {
        if (domain.responses) {
          const originalDomain = domain.domain;
          const convertedDomain = parseDomainName(domain.domain);
          console.log(`Domain conversion: "${originalDomain}" -> "${convertedDomain}"`);

          Object.entries(domain.responses).forEach(([questionId, answer]) => {
            partner2Responses.push({
              questionId,
              answer: answer as string,
              domain: convertedDomain // Convert formatted name to domain ID
            });
          });
        }
      });

      // Process individual assessments
      const partner1Assessment = processIndividualAssessment((couple as any).user_id_1, partner1Responses);
      const partner2Assessment = processIndividualAssessment((couple as any).user_id_2, partner2Responses);

      // Get partner names from profiles
      const partner1Profile = profiles?.find(p => p.id === couple.user_id_1);
      const partner2Profile = profiles?.find(p => p.id === couple.user_id_2);

      const partner1Name = (partner1Profile?.full_name as string) || (partner1Profile?.email as string) || "Partner 1";
      const partner2Name = (partner2Profile?.full_name as string) || (partner2Profile?.email as string) || "Partner 2";

      // Process couple compatibility with partner names
      const { analysisReport: report } = processCoupleAssessment(partner1Assessment, partner2Assessment, partner1Name, partner2Name);

      setAnalysisReport(report);
      setCoupleInfo({
        partner1Name,
        partner2Name,
      });

      // Save couple results to database
      await saveCoupleResults(supabase, couple.couple_id as string, report);

    } catch (err) {
      console.error("Error loading couple results:", err);
      setError(err instanceof Error ? err.message : "Failed to load results");
    } finally {
      setLoading(false);
    }
  };

  const saveCoupleResults = async (supabase: any, coupleId: string, report: CoupleAnalysisReport) => {
    try {
      console.log("Saving couple results for couple_id:", coupleId);

      // Check if couple results already exist
      const { data: existingResults, error: checkError } = await supabase
        .from("couple_results")
        .select("id")
        .eq("couple_id", coupleId)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        console.error("Error checking existing results:", checkError);
        throw checkError;
      }

      const resultsData = {
        couple_id: coupleId,
        overall_compatibility: report.overallCompatibility,
        compatibility_scores: report.domainAnalyses.reduce((acc: any, domain) => {
          acc[domain.domain] = domain.compatibilityScore;
          return acc;
        }, {}),
        alignment_areas: report.strengthAreas,
        conflict_areas: report.challengeAreas,
        updated_at: new Date().toISOString(),
      };

      console.log("Results data to save:", resultsData);

      if (existingResults) {
        // Update existing results
        console.log("Updating existing results with id:", existingResults.id);
        const { error: updateError } = await supabase
          .from("couple_results")
          .update(resultsData)
          .eq("id", existingResults.id);

        if (updateError) {
          console.error("Error updating couple results:", updateError);
          throw updateError;
        }
      } else {
        // Create new results
        console.log("Creating new couple results");
        const { error: insertError } = await supabase
          .from("couple_results")
          .insert(resultsData);

        if (insertError) {
          console.error("Error inserting couple results:", insertError);
          throw insertError;
        }
      }

      console.log("Successfully saved couple results");
    } catch (error) {
      console.error("Error saving couple results:", error);
      // Don't throw error here as the main functionality still works
    }
  };

  const downloadResults = () => {
    if (!analysisReport || !coupleInfo) return;

    const reportText = `
LAPORAN KOMPATIBILITAS PERNIKAHAN
=================================

${analysisReport.partner1Name || coupleInfo.partner1Name}
${analysisReport.partner2Name || coupleInfo.partner2Name}
Tanggal: ${new Date().toLocaleDateString('id-ID')}

KOMPATIBILITAS KESELURUHAN: ${analysisReport.overallCompatibility}% (${analysisReport.compatibilityLevel})

AREA KEKUATAN:
${analysisReport.strengthAreas.map(area => `- ${area}`).join('\n')}

AREA TANTANGAN:
${analysisReport.challengeAreas.map(area => `- ${area}`).join('\n')}

REKOMENDASI PRIORITAS:
${analysisReport.priorityRecommendations.map((rec, idx) => `${idx + 1}. ${rec}`).join('\n')}

ANALISIS DETAIL PER DOMAIN:
${analysisReport.domainAnalyses.map(domain => `
${domain.title}: ${domain.compatibilityScore}%
- ${analysisReport.partner1Name || 'Partner 1'}: ${domain.partner1Score}%
- ${analysisReport.partner2Name || 'Partner 2'}: ${domain.partner2Score}%
- Status: ${domain.status}
- Insights: ${domain.insights.join('; ')}
- Rekomendasi: ${domain.recommendations.join('; ')}
`).join('\n')}
    `;

    const blob = new Blob([reportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `laporan-kompatibilitas-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Menganalisis kompatibilitas...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4">
          <Button onClick={() => router.push("/couple/dashboard")} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali ke Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Hasil Kompatibilitas Pernikahan</h1>
            {coupleInfo && (
              <p className="text-muted-foreground mt-2">
                {coupleInfo.partner1Name} & {coupleInfo.partner2Name}
              </p>
            )}
          </div>
          <div className="flex gap-2">
            <Button onClick={downloadResults} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Download Laporan
            </Button>
            <Button onClick={() => router.push("/couple/dashboard")} variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Kembali
            </Button>
          </div>
        </div>
      </div>

      {analysisReport && (
        <EnhancedResultsVisualization 
          analysisReport={analysisReport}
          showCounselorNotes={false}
        />
      )}
    </div>
  );
}
