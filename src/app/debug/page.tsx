"use client";

import { useEffect, useState } from "react";
import { createClient } from "@/lib/supabase/client";

export default function DebugPage() {
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchData() {
      const supabase = createClient();
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        console.log("Current user:", user);

        // Get all couples
        const { data: couples, error: couplesError } = await supabase
          .from("couples")
          .select("*")
          .order("created_at", { ascending: false });

        console.log("Couples:", couples);
        console.log("Couples error:", couplesError);

        // Get all profiles
        const { data: profiles, error: profilesError } = await supabase
          .from("profiles")
          .select("*")
          .order("created_at", { ascending: false });

        console.log("Profiles:", profiles);
        console.log("Profiles error:", profilesError);

        // Get all individual results
        const { data: individualResults, error: individualError } = await supabase
          .from("individual_results")
          .select("*")
          .order("updated_at", { ascending: false });

        console.log("Individual results:", individualResults);
        console.log("Individual results error:", individualError);

        // Get all couple results
        const { data: coupleResults, error: coupleResultsError } = await supabase
          .from("couple_results")
          .select("*")
          .order("created_at", { ascending: false });

        console.log("Couple results:", coupleResults);
        console.log("Couple results error:", coupleResultsError);

        // Get all invitation codes
        const { data: codes, error: codesError } = await supabase
          .from("couple_invitation_codes")
          .select("*")
          .order("created_at", { ascending: false });

        console.log("Invitation codes:", codes);
        console.log("Invitation codes error:", codesError);

        setData({
          user,
          couples,
          profiles,
          individualResults,
          coupleResults,
          codes,
          errors: {
            couplesError,
            profilesError,
            individualError,
            coupleResultsError,
            codesError
          }
        });
      } catch (error) {
        console.error("Debug fetch error:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) {
    return <div className="p-8">Loading debug data...</div>;
  }

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Data</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Current User</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.user, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Couples ({data.couples?.length || 0})</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.couples, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Profiles ({data.profiles?.length || 0})</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.profiles, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Individual Results ({data.individualResults?.length || 0})</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.individualResults, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Couple Results ({data.coupleResults?.length || 0})</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.coupleResults, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Invitation Codes ({data.codes?.length || 0})</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.codes, null, 2)}
          </pre>
        </div>

        <div className="bg-red-100 p-4 rounded">
          <h2 className="font-bold mb-2">Errors</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(data.errors, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
