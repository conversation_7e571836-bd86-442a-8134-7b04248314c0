-- Fix RLS policies for couple_results table
-- This script adds missing INSERT and UPDATE policies for couple_results
-- Run this in Supabase SQL Editor

-- Check current policies for couple_results
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'couple_results'
ORDER BY cmd, policyname;

-- Add missing INSERT policy for couple_results
DROP POLICY IF EXISTS "Users can insert their couple results" ON couple_results;
CREATE POLICY "Users can insert their couple results"
  ON couple_results FOR INSERT
  WITH CHECK (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

-- Add missing UPDATE policy for couple_results
DROP POLICY IF EXISTS "Users can update their couple results" ON couple_results;
CREATE POLICY "Users can update their couple results"
  ON couple_results FOR UPDATE
  USING (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

-- Verify all policies are now in place
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'couple_results'
ORDER BY cmd, policyname;

-- Test if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'couple_results';
