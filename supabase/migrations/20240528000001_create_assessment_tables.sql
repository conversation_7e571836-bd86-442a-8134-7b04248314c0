-- Create tables for assessment data

-- Couples table (must be created first as it's referenced by other tables)
CREATE TABLE IF NOT EXISTS couples (
  couple_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id_1 UUID NOT NULL REFERENCES auth.users(id),
  user_id_2 UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual results table
CREATE TABLE IF NOT EXISTS individual_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  domains JSONB NOT NULL,
  overall_score INTEGER NOT NULL,
  current_progress JSONB DEFAULT '{}', -- Track progress per domain
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Couple results table
CREATE TABLE IF NOT EXISTS couple_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  couple_id UUID NOT NULL REFERENCES couples(couple_id),
  compatibility_scores JSONB NOT NULL,
  overall_compatibility INTEGER NOT NULL,
  alignment_areas JSONB NOT NULL,
  conflict_areas JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recommendations table
CREATE TABLE IF NOT EXISTS recommendations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  couple_id UUID NOT NULL REFERENCES couples(couple_id),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  domain TEXT NOT NULL,
  priority TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable row level security
ALTER TABLE individual_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE couple_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE recommendations ENABLE ROW LEVEL SECURITY;

-- Create policies
DROP POLICY IF EXISTS "Users can view their own individual results" ON individual_results;
CREATE POLICY "Users can view their own individual results"
  ON individual_results FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view their couple results" ON couple_results;
CREATE POLICY "Users can view their couple results"
  ON couple_results FOR SELECT
  USING (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

DROP POLICY IF EXISTS "Users can insert their couple results" ON couple_results;
CREATE POLICY "Users can insert their couple results"
  ON couple_results FOR INSERT
  WITH CHECK (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

DROP POLICY IF EXISTS "Users can update their couple results" ON couple_results;
CREATE POLICY "Users can update their couple results"
  ON couple_results FOR UPDATE
  USING (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

DROP POLICY IF EXISTS "Users can view their recommendations" ON recommendations;
CREATE POLICY "Users can view their recommendations"
  ON recommendations FOR SELECT
  USING (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

-- Enable realtime (only if not already added)
DO $$
BEGIN
    -- Add individual_results to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'individual_results'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE individual_results;
    END IF;

    -- Add couple_results to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'couple_results'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE couple_results;
    END IF;

    -- Add recommendations to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'recommendations'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE recommendations;
    END IF;
END $$;
