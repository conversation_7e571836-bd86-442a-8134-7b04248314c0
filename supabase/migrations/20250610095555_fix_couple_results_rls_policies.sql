-- Fix RLS policies for couple_results table
-- This migration adds missing INSERT and UPDATE policies for couple_results

-- Add missing INSERT policy for couple_results
DROP POLICY IF EXISTS "Users can insert their couple results" ON couple_results;
CREATE POLICY "Users can insert their couple results"
  ON couple_results FOR INSERT
  WITH CHECK (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

-- Add missing UPDATE policy for couple_results
DROP POLICY IF EXISTS "Users can update their couple results" ON couple_results;
CREATE POLICY "Users can update their couple results"
  ON couple_results FOR UPDATE
  USING (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

-- Ensure profiles policies allow partner access
DROP POLICY IF EXISTS "Users can view partner profiles" ON profiles;
CREATE POLICY "Users can view partner profiles"
  ON profiles FOR SELECT
  USING (
    auth.uid() = id
    OR EXISTS (
      SELECT 1 FROM couples
      WHERE (user_id_1 = auth.uid() AND user_id_2 = profiles.id)
         OR (user_id_2 = auth.uid() AND user_id_1 = profiles.id)
    )
  );