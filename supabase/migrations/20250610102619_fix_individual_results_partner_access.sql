-- Fix individual_results RLS policy to allow partner access
-- This allows users to see their partner's assessment results when they are connected as a couple

-- Drop the existing restrictive SELECT policy
DROP POLICY IF EXISTS "individual_results_select_policy" ON individual_results;

-- Create new policy that allows users to see their own results AND their partner's results
CREATE POLICY "individual_results_select_policy"
  ON individual_results FOR SELECT
  USING (
    auth.uid() = user_id
    OR EXISTS (
      SELECT 1 FROM couples
      WHERE (user_id_1 = auth.uid() AND user_id_2 = individual_results.user_id)
         OR (user_id_2 = auth.uid() AND user_id_1 = individual_results.user_id)
    )
  );

-- Also ensure the old policy name is dropped if it exists
DROP POLICY IF EXISTS "Users can view their own individual results" ON individual_results;