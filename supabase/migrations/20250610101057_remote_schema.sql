drop policy "Ad<PERSON> can insert admin_users" on "public"."admin_users";

drop policy "Ad<PERSON> can view all admin_users" on "public"."admin_users";

drop policy "Allow admin write access" on "public"."assessment_questions";

drop policy "Users can insert their couple results" on "public"."couple_results";

drop policy "Users can update their couple results" on "public"."couple_results";

drop policy "Users can view partner profiles" on "public"."profiles";

drop policy "Ad<PERSON> can manage all sessions" on "public"."counseling_sessions";

drop policy "Ad<PERSON> can manage all assignments" on "public"."counselor_couple_assignments";

drop policy "Admins can view all counselor profiles" on "public"."counselor_profiles";

alter table "public"."admin_users" drop constraint "admin_users_pkey";

drop index if exists "public"."admin_users_pkey";

alter table "public"."admin_users" add column "id" uuid not null default uuid_generate_v4();

alter table "public"."admin_users" add column "updated_at" timestamp with time zone default now();

alter table "public"."couples" enable row level security;

alter table "public"."profiles" drop column "avatar_url";

CREATE UNIQUE INDEX admin_users_pkey ON public.admin_users USING btree (id);

alter table "public"."admin_users" add constraint "admin_users_pkey" PRIMARY KEY using index "admin_users_pkey";

set check_function_bodies = off;

create or replace view "public"."couple_data" as  SELECT c.couple_id,
    u1.id AS user_id_1,
    p1.full_name AS user_name_1,
    p1.email AS user_email_1,
    u2.id AS user_id_2,
    p2.full_name AS user_name_2,
    p2.email AS user_email_2,
    c.created_at
   FROM ((((couples c
     JOIN auth.users u1 ON ((c.user_id_1 = u1.id)))
     JOIN auth.users u2 ON ((c.user_id_2 = u2.id)))
     JOIN profiles p1 ON ((u1.id = p1.id)))
     JOIN profiles p2 ON ((u2.id = p2.id)));


CREATE OR REPLACE FUNCTION public.get_user_couple()
 RETURNS TABLE(couple_id uuid, partner_id uuid, partner_name text, partner_email text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN QUERY
  SELECT 
    c.couple_id,
    CASE 
      WHEN c.user_id_1 = auth.uid() THEN c.user_id_2
      ELSE c.user_id_1
    END as partner_id,
    p.full_name as partner_name,
    p.email as partner_email
  FROM public.couples c
  JOIN public.profiles p ON 
    (c.user_id_1 = p.id OR c.user_id_2 = p.id) AND p.id != auth.uid()
  WHERE c.user_id_1 = auth.uid() OR c.user_id_2 = auth.uid()
  LIMIT 1;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_admin(user_uuid uuid)
 RETURNS boolean
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1 FROM admin_users 
    WHERE user_id = user_uuid
  );
$function$
;

create policy "Admin users can insert into admin table"
on "public"."admin_users"
as permissive
for insert
to public
with check (is_admin(auth.uid()));


create policy "Admin users can view admin table"
on "public"."admin_users"
as permissive
for select
to public
using (is_admin(auth.uid()));


create policy "Admins by JWT can delete from admin_users"
on "public"."admin_users"
as permissive
for delete
to authenticated
using ((( SELECT (users.raw_app_meta_data ->> 'userrole'::text)
   FROM auth.users
  WHERE (users.id = auth.uid())) = 'admin'::text));


create policy "Admins by JWT can insert into admin_users"
on "public"."admin_users"
as permissive
for insert
to authenticated
with check ((( SELECT (users.raw_app_meta_data ->> 'userrole'::text)
   FROM auth.users
  WHERE (users.id = auth.uid())) = 'admin'::text));


create policy "Admins by JWT can update admin_users"
on "public"."admin_users"
as permissive
for update
to authenticated
using ((( SELECT (users.raw_app_meta_data ->> 'userrole'::text)
   FROM auth.users
  WHERE (users.id = auth.uid())) = 'admin'::text))
with check ((( SELECT (users.raw_app_meta_data ->> 'userrole'::text)
   FROM auth.users
  WHERE (users.id = auth.uid())) = 'admin'::text));


create policy "Admins by JWT can view all admin_users"
on "public"."admin_users"
as permissive
for select
to authenticated
using ((( SELECT (users.raw_app_meta_data ->> 'userrole'::text)
   FROM auth.users
  WHERE (users.id = auth.uid())) = 'admin'::text));


create policy "Anyone can view admin_users"
on "public"."admin_users"
as permissive
for select
to authenticated
using (true);


create policy "Allow admin write access to assessment questions"
on "public"."assessment_questions"
as permissive
for all
to public
using (is_admin(auth.uid()));


create policy "Allow public read access to assessment questions"
on "public"."assessment_questions"
as permissive
for select
to public
using (true);


create policy "Counselors can update own sessions"
on "public"."counseling_sessions"
as permissive
for update
to public
using ((auth.uid() = counselor_id));


create policy "Counselors can view own sessions"
on "public"."counseling_sessions"
as permissive
for select
to public
using ((auth.uid() = counselor_id));


create policy "Counselors can view own assignments"
on "public"."counselor_couple_assignments"
as permissive
for select
to public
using ((auth.uid() = counselor_id));


create policy "Admins by JWT can view all counselor profiles"
on "public"."counselor_profiles"
as permissive
for select
to authenticated
using ((( SELECT (users.raw_app_meta_data ->> 'userrole'::text)
   FROM auth.users
  WHERE (users.id = auth.uid())) = 'admin'::text));


create policy "Admins can update counselor profiles"
on "public"."counselor_profiles"
as permissive
for update
to public
using (is_admin(auth.uid()));


create policy "Anyone can view counselor_profiles"
on "public"."counselor_profiles"
as permissive
for select
to authenticated
using (true);


create policy "Authenticated users can insert counselor_profiles"
on "public"."counselor_profiles"
as permissive
for insert
to authenticated
with check (true);


create policy "Counselors can update own profile"
on "public"."counselor_profiles"
as permissive
for update
to public
using ((auth.uid() = user_id));


create policy "Counselors can view own profile"
on "public"."counselor_profiles"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "Users can update invitation codes"
on "public"."couple_invitation_codes"
as permissive
for update
to public
using (((auth.uid() = creator_user_id) OR ((is_active = true) AND (expires_at > now()))))
with check (((auth.uid() = creator_user_id) OR (auth.uid() = used_by_user_id)));


create policy "Users can view active codes for validation"
on "public"."couple_invitation_codes"
as permissive
for select
to public
using (((auth.uid() = creator_user_id) OR (auth.uid() = used_by_user_id) OR ((is_active = true) AND (expires_at > now()))));


create policy "couples_insert_policy"
on "public"."couples"
as permissive
for insert
to public
with check (((auth.uid() = user_id_1) OR (auth.uid() = user_id_2)));


create policy "couples_select_policy"
on "public"."couples"
as permissive
for select
to public
using (((auth.uid() = user_id_1) OR (auth.uid() = user_id_2)));


create policy "individual_results_insert_policy"
on "public"."individual_results"
as permissive
for insert
to public
with check ((auth.uid() = user_id));


create policy "individual_results_select_policy"
on "public"."individual_results"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "individual_results_update_policy"
on "public"."individual_results"
as permissive
for update
to public
using ((auth.uid() = user_id));


create policy "profiles_insert_policy"
on "public"."profiles"
as permissive
for insert
to public
with check ((auth.uid() = id));


create policy "profiles_select_policy"
on "public"."profiles"
as permissive
for select
to public
using (true);


create policy "profiles_update_policy"
on "public"."profiles"
as permissive
for update
to public
using ((auth.uid() = id));


create policy "Admins can manage all sessions"
on "public"."counseling_sessions"
as permissive
for all
to public
using (is_admin(auth.uid()));


create policy "Admins can manage all assignments"
on "public"."counselor_couple_assignments"
as permissive
for all
to public
using (is_admin(auth.uid()));


create policy "Admins can view all counselor profiles"
on "public"."counselor_profiles"
as permissive
for select
to public
using (is_admin(auth.uid()));



