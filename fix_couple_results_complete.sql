-- Complete fix for couple results functionality
-- Run this in Supabase SQL Editor

-- 1. Check current state of tables and policies
SELECT 'Checking couples table...' as status;
SELECT COUNT(*) as couples_count FROM couples;

SELECT 'Checking profiles table...' as status;
SELECT COUNT(*) as profiles_count FROM profiles;

SELECT 'Checking individual_results table...' as status;
SELECT COUNT(*) as individual_results_count FROM individual_results;

SELECT 'Checking couple_results table...' as status;
SELECT COUNT(*) as couple_results_count FROM couple_results;

-- 2. Check current RLS policies for couple_results
SELECT 'Current couple_results policies:' as status;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'couple_results'
ORDER BY cmd, policyname;

-- 3. Add missing INSERT and UPDATE policies for couple_results
SELECT 'Adding missing policies for couple_results...' as status;

DROP POLICY IF EXISTS "Users can insert their couple results" ON couple_results;
CREATE POLICY "Users can insert their couple results"
  ON couple_results FOR INSERT
  WITH CHECK (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

DROP POLICY IF EXISTS "Users can update their couple results" ON couple_results;
CREATE POLICY "Users can update their couple results"
  ON couple_results FOR UPDATE
  USING (couple_id IN (
    SELECT couple_id FROM couples WHERE user_id_1 = auth.uid() OR user_id_2 = auth.uid()
  ));

-- 4. Ensure profiles policies allow partner access
SELECT 'Updating profiles policies...' as status;

DROP POLICY IF EXISTS "Users can view partner profiles" ON profiles;
CREATE POLICY "Users can view partner profiles"
  ON profiles FOR SELECT
  USING (
    auth.uid() = id 
    OR EXISTS (
      SELECT 1 FROM couples 
      WHERE (user_id_1 = auth.uid() AND user_id_2 = profiles.id)
         OR (user_id_2 = auth.uid() AND user_id_1 = profiles.id)
    )
  );

-- 5. Verify all policies are now in place
SELECT 'Final verification - couple_results policies:' as status;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'couple_results'
ORDER BY cmd, policyname;

SELECT 'Final verification - profiles policies:' as status;
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd,
  permissive
FROM pg_policies 
WHERE tablename = 'profiles'
ORDER BY cmd, policyname;

-- 6. Test data access (replace with actual user IDs if needed)
SELECT 'Testing couple data access...' as status;
SELECT 
  c.couple_id,
  c.user_id_1,
  c.user_id_2,
  p1.full_name as user1_name,
  p1.email as user1_email,
  p2.full_name as user2_name,
  p2.email as user2_email
FROM couples c
LEFT JOIN profiles p1 ON c.user_id_1 = p1.id
LEFT JOIN profiles p2 ON c.user_id_2 = p2.id
ORDER BY c.created_at DESC
LIMIT 3;

SELECT 'Setup complete!' as status;
