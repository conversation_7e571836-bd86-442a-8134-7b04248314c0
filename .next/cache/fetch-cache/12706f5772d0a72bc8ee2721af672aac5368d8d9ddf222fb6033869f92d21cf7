{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94d823a6efdf3fc5-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "<PERSON><PERSON>, 10 Jun 2025 10:20:59 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=FXs9pzR82n3Ob3rAu.GtfLc_JZJ4Eka2JrExS50ffBg-1749550859-*******-MGjvHKfrgncXIDw_6aGZUDn_KD4dsUUg3wT34Joe5kVmZb4DUxS98aZqchBCq9szvRb.Hop.hHqP4L5wGTamezCcJ9t_vu2x7.b46GwkvVg; path=/; expires=Tue, 10-Jun-25 10:50:59 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}