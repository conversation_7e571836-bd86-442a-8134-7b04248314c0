{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "94d809794b733f76-SIN", "connection": "keep-alive", "content-encoding": "gzip", "content-type": "application/json", "date": "<PERSON><PERSON>, 10 Jun 2025 10:03:07 GMT", "sb-gateway-version": "1", "sb-project-ref": "eqghwtejdnzgopmcjlho", "server": "cloudflare", "set-cookie": "__cf_bm=4Pe64nLDktownt_oFd.Hyy3KqKuQDpEDPamgQqtPpkE-1749549787-*******-ke8Be6qWiN6w_uHW.u6vXTtNaY76kBTbZ_t0zCR2BIvwPvtaAHAsBaaBCHe3d3Z5FxFBarbfTxBmJZss4Mvtp3tAD_zx.Fh_2.Vb75Ac6Ak; path=/; expires=Tue, 10-Jun-25 10:33:07 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Origin, Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "6"}, "body": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "status": 200, "url": "https://eqghwtejdnzgopmcjlho.supabase.co/auth/v1/user"}, "revalidate": 31536000, "tags": []}