"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/couple/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts":
/*!**********************************************!*\
  !*** ./src/lib/assessment/resultAnalysis.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCoupleAnalysisReport: function() { return /* binding */ generateCoupleAnalysisReport; }\n/* harmony export */ });\n// Domain titles and descriptions in Indonesian\nconst DOMAIN_INFO = {\n    \"visi-hidup\": {\n        title: \"Visi Hidup\",\n        description: \"Keselarasan tujuan dan aspirasi jangka panjang dalam pernikahan\"\n    },\n    keuangan: {\n        title: \"Keuangan\",\n        description: \"Pendekatan terhadap pengelolaan keuangan dan transparansi finansial\"\n    },\n    pengasuhan: {\n        title: \"Pengasuhan Anak\",\n        description: \"Gaya dan filosofi dalam mendidik dan membesarkan anak\"\n    },\n    komunikasi: {\n        title: \"Komunikasi\",\n        description: \"Cara berkomunikasi dan menyelesaikan konflik dalam hubungan\"\n    },\n    \"fungsi-dan-peran\": {\n        title: \"Fungsi dan Peran\",\n        description: \"Pemahaman tentang peran suami-istri berdasarkan nilai-nilai Alkitab\"\n    },\n    seks: {\n        title: \"Keintiman Seksual\",\n        description: \"Pandangan dan ekspektasi tentang keintiman dalam pernikahan\"\n    },\n    spiritualitas: {\n        title: \"Spiritualitas\",\n        description: \"Keselarasan dalam pertumbuhan iman dan praktik spiritual bersama\"\n    },\n    \"sisi-gelap\": {\n        title: \"Sisi Gelap\",\n        description: \"Pengelolaan emosi negatif dan potensi masalah dalam hubungan\"\n    }\n};\n// Generate comprehensive analysis report\nfunction generateCoupleAnalysisReport(compatibilityResult, partner1Name, partner2Name) {\n    const { partner1, partner2, compatibilityScores, overallCompatibility } = compatibilityResult;\n    // Determine compatibility level\n    const compatibilityLevel = getCompatibilityLevel(overallCompatibility);\n    // Generate domain analyses\n    const domainAnalyses = [];\n    Object.entries(compatibilityScores).forEach((param)=>{\n        let [domain, score] = param;\n        const analysis = generateDomainAnalysis(domain, partner1, partner2, score);\n        domainAnalyses.push(analysis);\n    });\n    // Identify strength and challenge areas\n    const strengthAreas = domainAnalyses.filter((d)=>d.status === \"aligned\").map((d)=>d.title);\n    const challengeAreas = domainAnalyses.filter((d)=>d.status === \"conflict\").map((d)=>d.title);\n    // Generate priority recommendations\n    const priorityRecommendations = generatePriorityRecommendations(domainAnalyses, partner1, partner2);\n    // Generate counselor notes\n    const counselorNotes = generateCounselorNotes(domainAnalyses, partner1, partner2);\n    return {\n        overallCompatibility,\n        compatibilityLevel,\n        domainAnalyses,\n        strengthAreas,\n        challengeAreas,\n        priorityRecommendations,\n        counselorNotes,\n        partner1Name,\n        partner2Name\n    };\n}\n// Generate analysis for a specific domain\nfunction generateDomainAnalysis(domain, partner1, partner2, compatibilityScore) {\n    var _partner1_domainScores_find, _partner2_domainScores_find;\n    const domainInfo = DOMAIN_INFO[domain];\n    const p1Score = ((_partner1_domainScores_find = partner1.domainScores.find((d)=>d.domain === domain)) === null || _partner1_domainScores_find === void 0 ? void 0 : _partner1_domainScores_find.score) || 0;\n    const p2Score = ((_partner2_domainScores_find = partner2.domainScores.find((d)=>d.domain === domain)) === null || _partner2_domainScores_find === void 0 ? void 0 : _partner2_domainScores_find.score) || 0;\n    const status = getCompatibilityStatus(compatibilityScore);\n    const insights = generateDomainInsights(domain, partner1, partner2, compatibilityScore);\n    const recommendations = generateDomainRecommendations(domain, partner1, partner2, status);\n    return {\n        domain,\n        title: (domainInfo === null || domainInfo === void 0 ? void 0 : domainInfo.title) || domain,\n        description: (domainInfo === null || domainInfo === void 0 ? void 0 : domainInfo.description) || \"\",\n        partner1Score: p1Score,\n        partner2Score: p2Score,\n        compatibilityScore,\n        status,\n        insights,\n        recommendations\n    };\n}\n// Determine compatibility status\nfunction getCompatibilityStatus(score) {\n    if (score >= 80) return \"aligned\";\n    if (score >= 60) return \"moderate\";\n    return \"conflict\";\n}\n// Get compatibility level description\nfunction getCompatibilityLevel(score) {\n    if (score >= 90) return \"Sangat Tinggi\";\n    if (score >= 80) return \"Tinggi\";\n    if (score >= 60) return \"Sedang\";\n    if (score >= 40) return \"Rendah\";\n    return \"Sangat Rendah\";\n}\n// Generate insights for specific domains\nfunction generateDomainInsights(domain, partner1, partner2, compatibilityScore, partner1Name, partner2Name) {\n    const insights = [];\n    switch(domain){\n        case \"pengasuhan\":\n            const p1ParentingStyle = partner1.categories[\"parenting-style\"];\n            const p2ParentingStyle = partner2.categories[\"parenting-style\"];\n            if (p1ParentingStyle && p2ParentingStyle) {\n                if (p1ParentingStyle === p2ParentingStyle) {\n                    insights.push(\"Kedua pasangan memiliki gaya pengasuhan yang sama: \".concat(p1ParentingStyle));\n                } else {\n                    insights.push(\"Perbedaan gaya pengasuhan: \".concat(partner1Name || \"Partner 1\", \" (\").concat(p1ParentingStyle, \") vs \").concat(partner2Name || \"Partner 2\", \" (\").concat(p2ParentingStyle, \")\"));\n                }\n            }\n            break;\n        case \"komunikasi\":\n            const p1CommStyle = partner1.categories[\"communication-style\"];\n            const p2CommStyle = partner2.categories[\"communication-style\"];\n            if (p1CommStyle && p2CommStyle) {\n                insights.push(\"Gaya komunikasi: \".concat(partner1Name || \"Partner 1\", \" (\").concat(p1CommStyle, \") vs \").concat(partner2Name || \"Partner 2\", \" (\").concat(p2CommStyle, \")\"));\n                if (p1CommStyle === \"Asertif\" && p2CommStyle === \"Asertif\") {\n                    insights.push(\"Kedua pasangan memiliki gaya komunikasi yang ideal untuk hubungan yang sehat\");\n                }\n            }\n            break;\n        case \"fungsi-dan-peran\":\n            const p1MaleRole = partner1.categories[\"biblical-male-role\"];\n            const p2MaleRole = partner2.categories[\"biblical-male-role\"];\n            if (p1MaleRole && p2MaleRole) {\n                insights.push(\"Pandangan tentang peran pria: \".concat(partner1Name || \"Partner 1\", \" (\").concat(p1MaleRole, \") vs \").concat(partner2Name || \"Partner 2\", \" (\").concat(p2MaleRole, \")\"));\n            }\n            break;\n        case \"sisi-gelap\":\n            const p1DarkEmotion = partner1.categories[\"negative-emotion\"];\n            const p2DarkEmotion = partner2.categories[\"negative-emotion\"];\n            if (p1DarkEmotion && p1DarkEmotion !== \"Tidak ada\") {\n                insights.push(\"\".concat(partner1Name || \"Partner 1\", \" cenderung mengalami \").concat(p1DarkEmotion.toLowerCase()));\n            }\n            if (p2DarkEmotion && p2DarkEmotion !== \"Tidak ada\") {\n                insights.push(\"\".concat(partner2Name || \"Partner 2\", \" cenderung mengalami \").concat(p2DarkEmotion.toLowerCase()));\n            }\n            break;\n    }\n    // Add general compatibility insight\n    if (compatibilityScore >= 80) {\n        insights.push(\"Area ini menunjukkan keselarasan yang baik antara kedua pasangan\");\n    } else if (compatibilityScore <= 50) {\n        insights.push(\"Area ini memerlukan perhatian khusus dan diskusi mendalam\");\n    }\n    return insights;\n}\n// Generate domain-specific recommendations\nfunction generateDomainRecommendations(domain, partner1, partner2, status) {\n    const recommendations = [];\n    if (status === \"conflict\") {\n        switch(domain){\n            case \"komunikasi\":\n                recommendations.push(\"Ikuti pelatihan komunikasi untuk pasangan\");\n                recommendations.push(\"Praktikkan teknik mendengarkan aktif\");\n                recommendations.push(\"Tetapkan aturan untuk diskusi yang konstruktif\");\n                break;\n            case \"pengasuhan\":\n                recommendations.push(\"Diskusikan filosofi pengasuhan sebelum memiliki anak\");\n                recommendations.push(\"Baca buku tentang pengasuhan bersama-sama\");\n                recommendations.push(\"Konsultasi dengan ahli pengasuhan anak\");\n                break;\n            case \"keuangan\":\n                recommendations.push(\"Buat rencana keuangan bersama\");\n                recommendations.push(\"Diskusikan transparansi keuangan\");\n                recommendations.push(\"Konsultasi dengan perencana keuangan\");\n                break;\n            case \"spiritualitas\":\n                recommendations.push(\"Diskusikan harapan spiritual dalam pernikahan\");\n                recommendations.push(\"Cari mentor spiritual untuk pasangan\");\n                recommendations.push(\"Rencanakan aktivitas spiritual bersama\");\n                break;\n        }\n    } else if (status === \"aligned\") {\n        recommendations.push(\"Pertahankan keselarasan yang sudah baik di area ini\");\n        recommendations.push(\"Gunakan kekuatan ini untuk mendukung area lain yang memerlukan perbaikan\");\n    }\n    return recommendations;\n}\n// Generate priority recommendations for the couple\nfunction generatePriorityRecommendations(domainAnalyses, partner1, partner2) {\n    const recommendations = [];\n    // Focus on conflict areas first\n    const conflictAreas = domainAnalyses.filter((d)=>d.status === \"conflict\");\n    if (conflictAreas.length > 0) {\n        recommendations.push(\"Prioritaskan diskusi mendalam tentang area-area konflik yang teridentifikasi\");\n        // Specific high-priority recommendations\n        if (conflictAreas.some((d)=>d.domain === \"komunikasi\")) {\n            recommendations.push(\"PRIORITAS TINGGI: Perbaiki pola komunikasi sebelum menikah\");\n        }\n        if (conflictAreas.some((d)=>d.domain === \"fungsi-dan-peran\")) {\n            recommendations.push(\"PRIORITAS TINGGI: Klarifikasi ekspektasi peran dalam pernikahan\");\n        }\n    }\n    // Add general recommendations\n    recommendations.push(\"Lakukan sesi konseling pra-nikah dengan konselor yang berpengalaman\");\n    recommendations.push(\"Buat rencana konkret untuk mengatasi area-area yang memerlukan perbaikan\");\n    return recommendations;\n}\n// Generate notes for counselors\nfunction generateCounselorNotes(domainAnalyses, partner1, partner2) {\n    const notes = [];\n    // Overall assessment\n    const conflictCount = domainAnalyses.filter((d)=>d.status === \"conflict\").length;\n    const alignedCount = domainAnalyses.filter((d)=>d.status === \"aligned\").length;\n    notes.push(\"Jumlah area konflik: \".concat(conflictCount, \"/8\"));\n    notes.push(\"Jumlah area selaras: \".concat(alignedCount, \"/8\"));\n    // Specific counselor guidance\n    if (conflictCount >= 4) {\n        notes.push(\"PERHATIAN: Banyak area konflik terdeteksi. Pertimbangkan sesi konseling intensif.\");\n    }\n    // Check for critical combinations\n    const criticalDomains = [\n        \"komunikasi\",\n        \"fungsi-dan-peran\",\n        \"spiritualitas\"\n    ];\n    const criticalConflicts = domainAnalyses.filter((d)=>criticalDomains.includes(d.domain) && d.status === \"conflict\");\n    if (criticalConflicts.length >= 2) {\n        notes.push(\"PERINGATAN: Konflik di area-area fundamental pernikahan terdeteksi.\");\n    }\n    return notes;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts\n"));

/***/ })

});