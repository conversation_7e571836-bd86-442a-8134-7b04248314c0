"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/couple/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/assessment/assessmentUtils.ts":
/*!***********************************************!*\
  !*** ./src/lib/assessment/assessmentUtils.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ASSESSMENT_DOMAINS: function() { return /* reexport safe */ _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS; },\n/* harmony export */   convertDomainName: function() { return /* binding */ convertDomainName; },\n/* harmony export */   formatDomainName: function() { return /* binding */ formatDomainName; },\n/* harmony export */   formatResponsesForDatabase: function() { return /* binding */ formatResponsesForDatabase; },\n/* harmony export */   generateCounselorSummary: function() { return /* binding */ generateCounselorSummary; },\n/* harmony export */   getAllDomains: function() { return /* binding */ getAllDomains; },\n/* harmony export */   getAssessmentProgress: function() { return /* binding */ getAssessmentProgress; },\n/* harmony export */   getDomainCompletionStatus: function() { return /* binding */ getDomainCompletionStatus; },\n/* harmony export */   getQuestionsForDomain: function() { return /* binding */ getQuestionsForDomain; },\n/* harmony export */   isDomainCompleted: function() { return /* binding */ isDomainCompleted; },\n/* harmony export */   parseDomainName: function() { return /* binding */ parseDomainName; },\n/* harmony export */   parseResponsesFromDatabase: function() { return /* binding */ parseResponsesFromDatabase; },\n/* harmony export */   processCoupleAssessment: function() { return /* binding */ processCoupleAssessment; },\n/* harmony export */   processIndividualAssessment: function() { return /* binding */ processIndividualAssessment; },\n/* harmony export */   validateResponses: function() { return /* binding */ validateResponses; }\n/* harmony export */ });\n/* harmony import */ var _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./enhancedQuestions */ \"(app-pages-browser)/./src/lib/assessment/enhancedQuestions.ts\");\n/* harmony import */ var _calculationLogic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./calculationLogic */ \"(app-pages-browser)/./src/lib/assessment/calculationLogic.ts\");\n/* harmony import */ var _resultAnalysis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resultAnalysis */ \"(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts\");\n\n\n\n// Utility functions for the assessment system\n// Get all questions for a specific domain\nfunction getQuestionsForDomain(domain) {\n    return _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain] || [];\n}\n// Get all domains\nfunction getAllDomains() {\n    return _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS;\n}\n// Format domain name for display\nfunction formatDomainName(domain) {\n    const domainNames = {\n        \"visi-hidup\": \"Visi Hidup\",\n        keuangan: \"Keuangan\",\n        pengasuhan: \"Pengasuhan Anak\",\n        komunikasi: \"Komunikasi\",\n        \"fungsi-dan-peran\": \"Fungsi dan Peran\",\n        seks: \"Keintiman Seksual\",\n        spiritualitas: \"Spiritualitas\",\n        \"sisi-gelap\": \"Sisi Gelap\"\n    };\n    return domainNames[domain] || domain;\n}\n// Convert formatted domain name back to domain ID\nfunction parseDomainName(formattedName) {\n    const domainIds = {\n        \"Visi Hidup\": \"visi-hidup\",\n        Keuangan: \"keuangan\",\n        \"Pengasuhan Anak\": \"pengasuhan\",\n        Komunikasi: \"komunikasi\",\n        \"Fungsi dan Peran\": \"fungsi-dan-peran\",\n        \"Keintiman Seksual\": \"seks\",\n        Spiritualitas: \"spiritualitas\",\n        \"Sisi Gelap\": \"sisi-gelap\"\n    };\n    return domainIds[formattedName] || formattedName;\n}\n// Convert between Indonesian and English domain names\nfunction convertDomainName(domain, toLanguage) {\n    const idToEn = {\n        \"visi-hidup\": \"vision\",\n        keuangan: \"finances\",\n        pengasuhan: \"parenting\",\n        komunikasi: \"communication\",\n        \"fungsi-dan-peran\": \"roles\",\n        seks: \"sexuality\",\n        spiritualitas: \"spirituality\",\n        \"sisi-gelap\": \"darkside\"\n    };\n    const enToId = {\n        vision: \"visi-hidup\",\n        finances: \"keuangan\",\n        parenting: \"pengasuhan\",\n        communication: \"komunikasi\",\n        roles: \"fungsi-dan-peran\",\n        sexuality: \"seks\",\n        spirituality: \"spiritualitas\",\n        darkside: \"sisi-gelap\"\n    };\n    if (toLanguage === \"en\") {\n        return idToEn[domain] || domain;\n    } else {\n        return enToId[domain] || domain;\n    }\n}\n// Check if domain is completed based on formatted names from database\nfunction isDomainCompleted(domainName, completedDomains) {\n    if (!completedDomains || completedDomains.length === 0) {\n        return false;\n    }\n    // Try exact match first\n    if (completedDomains.includes(domainName)) {\n        return true;\n    }\n    // Try formatted name match (this is how data is stored in DB)\n    const formattedName = formatDomainName(domainName);\n    if (completedDomains.includes(formattedName)) {\n        return true;\n    }\n    // Try converted name match\n    const convertedToEn = convertDomainName(domainName, \"en\");\n    if (completedDomains.includes(convertedToEn)) {\n        return true;\n    }\n    const convertedToId = convertDomainName(domainName, \"id\");\n    if (completedDomains.includes(convertedToId)) {\n        return true;\n    }\n    // Try formatted versions of converted names\n    const formattedConverted = formatDomainName(convertedToId);\n    if (completedDomains.includes(formattedConverted)) {\n        return true;\n    }\n    // Try case-insensitive match for all variations\n    const lowerDomainName = domainName.toLowerCase();\n    return completedDomains.some((completed)=>{\n        const lowerCompleted = completed.toLowerCase();\n        return lowerCompleted === lowerDomainName || lowerCompleted === formattedName.toLowerCase() || lowerCompleted === convertedToEn.toLowerCase() || lowerCompleted === convertedToId.toLowerCase() || lowerCompleted === formattedConverted.toLowerCase();\n    });\n}\n// Validate assessment responses\nfunction validateResponses(responses) {\n    const missingDomains = [];\n    const missingQuestions = [];\n    // Check if all domains are covered\n    _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.forEach((domain)=>{\n        const domainResponses = responses.filter((r)=>r.domain === domain);\n        const domainQuestions = _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain];\n        if (domainResponses.length === 0) {\n            missingDomains.push(domain);\n        } else {\n            // Check if all required questions are answered\n            domainQuestions.forEach((question)=>{\n                if (question.required) {\n                    const hasResponse = domainResponses.some((r)=>r.questionId === question.id);\n                    if (!hasResponse) {\n                        missingQuestions.push(question.id);\n                    }\n                }\n            });\n        }\n    });\n    return {\n        isValid: missingDomains.length === 0 && missingQuestions.length === 0,\n        missingDomains,\n        missingQuestions\n    };\n}\n// Process complete assessment for an individual\nfunction processIndividualAssessment(userId, responses) {\n    const validation = validateResponses(responses);\n    if (!validation.isValid) {\n        throw new Error(\"Assessment incomplete. Missing domains: \".concat(validation.missingDomains.join(\", \"), \". \") + \"Missing questions: \".concat(validation.missingQuestions.join(\", \")));\n    }\n    return (0,_calculationLogic__WEBPACK_IMPORTED_MODULE_1__.calculateIndividualResult)(userId, responses);\n}\n// Process couple compatibility assessment\nfunction processCoupleAssessment(partner1Result, partner2Result, partner1Name, partner2Name) {\n    const compatibility = (0,_calculationLogic__WEBPACK_IMPORTED_MODULE_1__.calculateCompatibility)(partner1Result, partner2Result);\n    const analysisReport = (0,_resultAnalysis__WEBPACK_IMPORTED_MODULE_2__.generateCoupleAnalysisReport)(compatibility, partner1Name, partner2Name);\n    return {\n        compatibility,\n        analysisReport\n    };\n}\n// Get progress for an individual's assessment\nfunction getAssessmentProgress(responses) {\n    const completedDomains = [];\n    _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.forEach((domain)=>{\n        const domainQuestions = _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain];\n        const requiredQuestions = domainQuestions.filter((q)=>q.required);\n        const domainResponses = responses.filter((r)=>r.domain === domain);\n        // Check if all required questions are answered\n        const allRequiredAnswered = requiredQuestions.every((question)=>domainResponses.some((response)=>response.questionId === question.id));\n        if (allRequiredAnswered) {\n            completedDomains.push(domain);\n        }\n    });\n    const progressPercentage = Math.round(completedDomains.length / _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.length * 100);\n    // Find next incomplete domain\n    const nextDomain = _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.find((domain)=>!completedDomains.includes(domain));\n    return {\n        completedDomains,\n        totalDomains: _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.length,\n        progressPercentage,\n        nextDomain\n    };\n}\n// Generate summary for counselor dashboard\nfunction generateCounselorSummary(analysisReport) {\n    const { overallCompatibility, challengeAreas, domainAnalyses } = analysisReport;\n    // Determine risk level\n    let riskLevel;\n    if (overallCompatibility >= 80) riskLevel = \"Low\";\n    else if (overallCompatibility >= 60) riskLevel = \"Medium\";\n    else if (overallCompatibility >= 40) riskLevel = \"High\";\n    else riskLevel = \"Critical\";\n    // Generate key insights\n    const keyInsights = [];\n    if (challengeAreas.length === 0) {\n        keyInsights.push(\"Pasangan menunjukkan keselarasan yang baik di semua area\");\n    } else {\n        keyInsights.push(\"\".concat(challengeAreas.length, \" area memerlukan perhatian khusus\"));\n    }\n    // Check for critical patterns\n    const communicationIssues = domainAnalyses.find((d)=>d.domain === \"komunikasi\" && d.status === \"conflict\");\n    if (communicationIssues) {\n        keyInsights.push(\"Masalah komunikasi terdeteksi - prioritas utama untuk ditangani\");\n    }\n    const roleConflicts = domainAnalyses.find((d)=>d.domain === \"fungsi-dan-peran\" && d.status === \"conflict\");\n    if (roleConflicts) {\n        keyInsights.push(\"Perbedaan pandangan tentang peran dalam pernikahan perlu didiskusikan\");\n    }\n    // Generate action items\n    const actionItems = [];\n    challengeAreas.forEach((area)=>{\n        actionItems.push(\"Sesi khusus untuk membahas \".concat(area));\n    });\n    if (riskLevel === \"Critical\" || riskLevel === \"High\") {\n        actionItems.push(\"Pertimbangkan sesi konseling intensif\");\n        actionItems.push(\"Evaluasi kesiapan untuk menikah\");\n    }\n    // Generate session recommendations\n    const sessionRecommendations = [];\n    if (challengeAreas.length > 0) {\n        sessionRecommendations.push(\"Mulai dengan area prioritas: \".concat(challengeAreas[0]));\n    }\n    sessionRecommendations.push(\"Gunakan hasil assessment sebagai panduan diskusi\");\n    sessionRecommendations.push(\"Fokus pada solusi praktis dan rencana tindakan\");\n    if (riskLevel === \"Low\") {\n        sessionRecommendations.push(\"Sesi follow-up dalam 3-6 bulan\");\n    } else {\n        sessionRecommendations.push(\"Sesi follow-up dalam 1-2 bulan\");\n    }\n    return {\n        riskLevel,\n        keyInsights,\n        actionItems,\n        sessionRecommendations\n    };\n}\n// Helper function to convert responses to database format\nfunction formatResponsesForDatabase(responses) {\n    return responses.map((response)=>({\n            question_id: response.questionId,\n            answer: typeof response.answer === \"object\" ? JSON.stringify(response.answer) : response.answer.toString(),\n            domain: response.domain\n        }));\n}\n// Helper function to parse responses from database format\nfunction parseResponsesFromDatabase(dbResponses) {\n    return dbResponses.map((dbResponse)=>({\n            questionId: dbResponse.question_id,\n            answer: dbResponse.answer,\n            domain: dbResponse.domain\n        }));\n}\n// Get domain completion status\nfunction getDomainCompletionStatus(domain, responses) {\n    const domainQuestions = _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain] || [];\n    const domainResponses = responses.filter((r)=>r.domain === domain);\n    const requiredQuestions = domainQuestions.filter((q)=>q.required);\n    const answeredRequired = requiredQuestions.filter((question)=>domainResponses.some((response)=>response.questionId === question.id)).length;\n    const isComplete = answeredRequired === requiredQuestions.length;\n    return {\n        isComplete,\n        answeredQuestions: domainResponses.length,\n        totalQuestions: domainQuestions.length,\n        requiredQuestions: requiredQuestions.length,\n        answeredRequired\n    };\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/assessment/assessmentUtils.ts\n"));

/***/ })

});