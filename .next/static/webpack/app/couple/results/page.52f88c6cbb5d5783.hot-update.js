"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/couple/results/page",{

/***/ "(app-pages-browser)/./src/lib/assessment/assessmentUtils.ts":
/*!***********************************************!*\
  !*** ./src/lib/assessment/assessmentUtils.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ASSESSMENT_DOMAINS: function() { return /* reexport safe */ _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS; },\n/* harmony export */   convertDomainName: function() { return /* binding */ convertDomainName; },\n/* harmony export */   formatDomainName: function() { return /* binding */ formatDomainName; },\n/* harmony export */   formatResponsesForDatabase: function() { return /* binding */ formatResponsesForDatabase; },\n/* harmony export */   generateCounselorSummary: function() { return /* binding */ generateCounselorSummary; },\n/* harmony export */   getAllDomains: function() { return /* binding */ getAllDomains; },\n/* harmony export */   getAssessmentProgress: function() { return /* binding */ getAssessmentProgress; },\n/* harmony export */   getDomainCompletionStatus: function() { return /* binding */ getDomainCompletionStatus; },\n/* harmony export */   getQuestionsForDomain: function() { return /* binding */ getQuestionsForDomain; },\n/* harmony export */   isDomainCompleted: function() { return /* binding */ isDomainCompleted; },\n/* harmony export */   parseDomainName: function() { return /* binding */ parseDomainName; },\n/* harmony export */   parseResponsesFromDatabase: function() { return /* binding */ parseResponsesFromDatabase; },\n/* harmony export */   processCoupleAssessment: function() { return /* binding */ processCoupleAssessment; },\n/* harmony export */   processIndividualAssessment: function() { return /* binding */ processIndividualAssessment; },\n/* harmony export */   validateResponses: function() { return /* binding */ validateResponses; }\n/* harmony export */ });\n/* harmony import */ var _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./enhancedQuestions */ \"(app-pages-browser)/./src/lib/assessment/enhancedQuestions.ts\");\n/* harmony import */ var _calculationLogic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./calculationLogic */ \"(app-pages-browser)/./src/lib/assessment/calculationLogic.ts\");\n/* harmony import */ var _resultAnalysis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resultAnalysis */ \"(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts\");\n\n\n\n// Utility functions for the assessment system\n// Get all questions for a specific domain\nfunction getQuestionsForDomain(domain) {\n    return _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain] || [];\n}\n// Get all domains\nfunction getAllDomains() {\n    return _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS;\n}\n// Format domain name for display\nfunction formatDomainName(domain) {\n    const domainNames = {\n        \"visi-hidup\": \"Visi Hidup\",\n        keuangan: \"Keuangan\",\n        pengasuhan: \"Pengasuhan Anak\",\n        komunikasi: \"Komunikasi\",\n        \"fungsi-dan-peran\": \"Fungsi dan Peran\",\n        seks: \"Keintiman Seksual\",\n        spiritualitas: \"Spiritualitas\",\n        \"sisi-gelap\": \"Sisi Gelap\"\n    };\n    return domainNames[domain] || domain;\n}\n// Convert formatted domain name back to domain ID\nfunction parseDomainName(formattedName) {\n    const domainIds = {\n        \"Visi Hidup\": \"visi-hidup\",\n        Keuangan: \"keuangan\",\n        \"Pengasuhan Anak\": \"pengasuhan\",\n        Komunikasi: \"komunikasi\",\n        \"Fungsi dan Peran\": \"fungsi-dan-peran\",\n        \"Keintiman Seksual\": \"seks\",\n        Spiritualitas: \"spiritualitas\",\n        \"Sisi Gelap\": \"sisi-gelap\"\n    };\n    return domainIds[formattedName] || formattedName;\n}\n// Convert between Indonesian and English domain names\nfunction convertDomainName(domain, toLanguage) {\n    const idToEn = {\n        \"visi-hidup\": \"vision\",\n        keuangan: \"finances\",\n        pengasuhan: \"parenting\",\n        komunikasi: \"communication\",\n        \"fungsi-dan-peran\": \"roles\",\n        seks: \"sexuality\",\n        spiritualitas: \"spirituality\",\n        \"sisi-gelap\": \"darkside\"\n    };\n    const enToId = {\n        vision: \"visi-hidup\",\n        finances: \"keuangan\",\n        parenting: \"pengasuhan\",\n        communication: \"komunikasi\",\n        roles: \"fungsi-dan-peran\",\n        sexuality: \"seks\",\n        spirituality: \"spiritualitas\",\n        darkside: \"sisi-gelap\"\n    };\n    if (toLanguage === \"en\") {\n        return idToEn[domain] || domain;\n    } else {\n        return enToId[domain] || domain;\n    }\n}\n// Check if domain is completed based on formatted names from database\nfunction isDomainCompleted(domainName, completedDomains) {\n    if (!completedDomains || completedDomains.length === 0) {\n        return false;\n    }\n    // Try exact match first\n    if (completedDomains.includes(domainName)) {\n        return true;\n    }\n    // Try formatted name match (this is how data is stored in DB)\n    const formattedName = formatDomainName(domainName);\n    if (completedDomains.includes(formattedName)) {\n        return true;\n    }\n    // Try converted name match\n    const convertedToEn = convertDomainName(domainName, \"en\");\n    if (completedDomains.includes(convertedToEn)) {\n        return true;\n    }\n    const convertedToId = convertDomainName(domainName, \"id\");\n    if (completedDomains.includes(convertedToId)) {\n        return true;\n    }\n    // Try formatted versions of converted names\n    const formattedConverted = formatDomainName(convertedToId);\n    if (completedDomains.includes(formattedConverted)) {\n        return true;\n    }\n    // Try case-insensitive match for all variations\n    const lowerDomainName = domainName.toLowerCase();\n    return completedDomains.some((completed)=>{\n        const lowerCompleted = completed.toLowerCase();\n        return lowerCompleted === lowerDomainName || lowerCompleted === formattedName.toLowerCase() || lowerCompleted === convertedToEn.toLowerCase() || lowerCompleted === convertedToId.toLowerCase() || lowerCompleted === formattedConverted.toLowerCase();\n    });\n}\n// Validate assessment responses\nfunction validateResponses(responses) {\n    const missingDomains = [];\n    const missingQuestions = [];\n    // Check if all domains are covered\n    _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.forEach((domain)=>{\n        const domainResponses = responses.filter((r)=>r.domain === domain);\n        const domainQuestions = _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain];\n        if (domainResponses.length === 0) {\n            missingDomains.push(domain);\n        } else {\n            // Check if all required questions are answered\n            domainQuestions.forEach((question)=>{\n                if (question.required) {\n                    const hasResponse = domainResponses.some((r)=>r.questionId === question.id);\n                    if (!hasResponse) {\n                        missingQuestions.push(question.id);\n                    }\n                }\n            });\n        }\n    });\n    return {\n        isValid: missingDomains.length === 0 && missingQuestions.length === 0,\n        missingDomains,\n        missingQuestions\n    };\n}\n// Process complete assessment for an individual\nfunction processIndividualAssessment(userId, responses) {\n    const validation = validateResponses(responses);\n    if (!validation.isValid) {\n        throw new Error(\"Assessment incomplete. Missing domains: \".concat(validation.missingDomains.join(\", \"), \". \") + \"Missing questions: \".concat(validation.missingQuestions.join(\", \")));\n    }\n    return (0,_calculationLogic__WEBPACK_IMPORTED_MODULE_1__.calculateIndividualResult)(userId, responses);\n}\n// Process couple compatibility assessment\nfunction processCoupleAssessment(partner1Result, partner2Result) {\n    const compatibility = (0,_calculationLogic__WEBPACK_IMPORTED_MODULE_1__.calculateCompatibility)(partner1Result, partner2Result);\n    const analysisReport = (0,_resultAnalysis__WEBPACK_IMPORTED_MODULE_2__.generateCoupleAnalysisReport)(compatibility);\n    return {\n        compatibility,\n        analysisReport\n    };\n}\n// Get progress for an individual's assessment\nfunction getAssessmentProgress(responses) {\n    const completedDomains = [];\n    _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.forEach((domain)=>{\n        const domainQuestions = _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain];\n        const requiredQuestions = domainQuestions.filter((q)=>q.required);\n        const domainResponses = responses.filter((r)=>r.domain === domain);\n        // Check if all required questions are answered\n        const allRequiredAnswered = requiredQuestions.every((question)=>domainResponses.some((response)=>response.questionId === question.id));\n        if (allRequiredAnswered) {\n            completedDomains.push(domain);\n        }\n    });\n    const progressPercentage = Math.round(completedDomains.length / _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.length * 100);\n    // Find next incomplete domain\n    const nextDomain = _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.find((domain)=>!completedDomains.includes(domain));\n    return {\n        completedDomains,\n        totalDomains: _calculationLogic__WEBPACK_IMPORTED_MODULE_1__.ASSESSMENT_DOMAINS.length,\n        progressPercentage,\n        nextDomain\n    };\n}\n// Generate summary for counselor dashboard\nfunction generateCounselorSummary(analysisReport) {\n    const { overallCompatibility, challengeAreas, domainAnalyses } = analysisReport;\n    // Determine risk level\n    let riskLevel;\n    if (overallCompatibility >= 80) riskLevel = \"Low\";\n    else if (overallCompatibility >= 60) riskLevel = \"Medium\";\n    else if (overallCompatibility >= 40) riskLevel = \"High\";\n    else riskLevel = \"Critical\";\n    // Generate key insights\n    const keyInsights = [];\n    if (challengeAreas.length === 0) {\n        keyInsights.push(\"Pasangan menunjukkan keselarasan yang baik di semua area\");\n    } else {\n        keyInsights.push(\"\".concat(challengeAreas.length, \" area memerlukan perhatian khusus\"));\n    }\n    // Check for critical patterns\n    const communicationIssues = domainAnalyses.find((d)=>d.domain === \"komunikasi\" && d.status === \"conflict\");\n    if (communicationIssues) {\n        keyInsights.push(\"Masalah komunikasi terdeteksi - prioritas utama untuk ditangani\");\n    }\n    const roleConflicts = domainAnalyses.find((d)=>d.domain === \"fungsi-dan-peran\" && d.status === \"conflict\");\n    if (roleConflicts) {\n        keyInsights.push(\"Perbedaan pandangan tentang peran dalam pernikahan perlu didiskusikan\");\n    }\n    // Generate action items\n    const actionItems = [];\n    challengeAreas.forEach((area)=>{\n        actionItems.push(\"Sesi khusus untuk membahas \".concat(area));\n    });\n    if (riskLevel === \"Critical\" || riskLevel === \"High\") {\n        actionItems.push(\"Pertimbangkan sesi konseling intensif\");\n        actionItems.push(\"Evaluasi kesiapan untuk menikah\");\n    }\n    // Generate session recommendations\n    const sessionRecommendations = [];\n    if (challengeAreas.length > 0) {\n        sessionRecommendations.push(\"Mulai dengan area prioritas: \".concat(challengeAreas[0]));\n    }\n    sessionRecommendations.push(\"Gunakan hasil assessment sebagai panduan diskusi\");\n    sessionRecommendations.push(\"Fokus pada solusi praktis dan rencana tindakan\");\n    if (riskLevel === \"Low\") {\n        sessionRecommendations.push(\"Sesi follow-up dalam 3-6 bulan\");\n    } else {\n        sessionRecommendations.push(\"Sesi follow-up dalam 1-2 bulan\");\n    }\n    return {\n        riskLevel,\n        keyInsights,\n        actionItems,\n        sessionRecommendations\n    };\n}\n// Helper function to convert responses to database format\nfunction formatResponsesForDatabase(responses) {\n    return responses.map((response)=>({\n            question_id: response.questionId,\n            answer: typeof response.answer === \"object\" ? JSON.stringify(response.answer) : response.answer.toString(),\n            domain: response.domain\n        }));\n}\n// Helper function to parse responses from database format\nfunction parseResponsesFromDatabase(dbResponses) {\n    return dbResponses.map((dbResponse)=>({\n            questionId: dbResponse.question_id,\n            answer: dbResponse.answer,\n            domain: dbResponse.domain\n        }));\n}\n// Get domain completion status\nfunction getDomainCompletionStatus(domain, responses) {\n    const domainQuestions = _enhancedQuestions__WEBPACK_IMPORTED_MODULE_0__.enhancedAssessmentQuestions[domain] || [];\n    const domainResponses = responses.filter((r)=>r.domain === domain);\n    const requiredQuestions = domainQuestions.filter((q)=>q.required);\n    const answeredRequired = requiredQuestions.filter((question)=>domainResponses.some((response)=>response.questionId === question.id)).length;\n    const isComplete = answeredRequired === requiredQuestions.length;\n    return {\n        isComplete,\n        answeredQuestions: domainResponses.length,\n        totalQuestions: domainQuestions.length,\n        requiredQuestions: requiredQuestions.length,\n        answeredRequired\n    };\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/assessment/assessmentUtils.ts\n"));

/***/ })

});