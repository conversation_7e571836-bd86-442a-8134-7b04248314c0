"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/couple/results/page",{

/***/ "(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts":
/*!**********************************************!*\
  !*** ./src/lib/assessment/resultAnalysis.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCoupleAnalysisReport: function() { return /* binding */ generateCoupleAnalysisReport; }\n/* harmony export */ });\n// Domain titles and descriptions in Indonesian\nconst DOMAIN_INFO = {\n    \"visi-hidup\": {\n        title: \"Visi Hidup\",\n        description: \"Keselarasan tujuan dan aspirasi jangka panjang dalam pernikahan\"\n    },\n    keuangan: {\n        title: \"Keuangan\",\n        description: \"Pendekatan terhadap pengelolaan keuangan dan transparansi finansial\"\n    },\n    pengasuhan: {\n        title: \"Pengasuhan Anak\",\n        description: \"Gaya dan filosofi dalam mendidik dan membesarkan anak\"\n    },\n    komunikasi: {\n        title: \"Komunikasi\",\n        description: \"Cara berkomunikasi dan menyelesaikan konflik dalam hubungan\"\n    },\n    \"fungsi-dan-peran\": {\n        title: \"Fungsi dan Peran\",\n        description: \"Pemahaman tentang peran suami-istri berdasarkan nilai-nilai Alkitab\"\n    },\n    seks: {\n        title: \"Keintiman Seksual\",\n        description: \"Pandangan dan ekspektasi tentang keintiman dalam pernikahan\"\n    },\n    spiritualitas: {\n        title: \"Spiritualitas\",\n        description: \"Keselarasan dalam pertumbuhan iman dan praktik spiritual bersama\"\n    },\n    \"sisi-gelap\": {\n        title: \"Sisi Gelap\",\n        description: \"Pengelolaan emosi negatif dan potensi masalah dalam hubungan\"\n    }\n};\n// Generate comprehensive analysis report\nfunction generateCoupleAnalysisReport(compatibilityResult, partner1Name, partner2Name) {\n    const { partner1, partner2, compatibilityScores, overallCompatibility } = compatibilityResult;\n    // Determine compatibility level\n    const compatibilityLevel = getCompatibilityLevel(overallCompatibility);\n    // Generate domain analyses\n    const domainAnalyses = [];\n    Object.entries(compatibilityScores).forEach((param)=>{\n        let [domain, score] = param;\n        const analysis = generateDomainAnalysis(domain, partner1, partner2, score);\n        domainAnalyses.push(analysis);\n    });\n    // Identify strength and challenge areas\n    const strengthAreas = domainAnalyses.filter((d)=>d.status === \"aligned\").map((d)=>d.title);\n    const challengeAreas = domainAnalyses.filter((d)=>d.status === \"conflict\").map((d)=>d.title);\n    // Generate priority recommendations\n    const priorityRecommendations = generatePriorityRecommendations(domainAnalyses, partner1, partner2);\n    // Generate counselor notes\n    const counselorNotes = generateCounselorNotes(domainAnalyses, partner1, partner2);\n    return {\n        overallCompatibility,\n        compatibilityLevel,\n        domainAnalyses,\n        strengthAreas,\n        challengeAreas,\n        priorityRecommendations,\n        counselorNotes,\n        partner1Name,\n        partner2Name\n    };\n}\n// Generate analysis for a specific domain\nfunction generateDomainAnalysis(domain, partner1, partner2, compatibilityScore, partner1Name, partner2Name) {\n    var _partner1_domainScores_find, _partner2_domainScores_find;\n    const domainInfo = DOMAIN_INFO[domain];\n    const p1Score = ((_partner1_domainScores_find = partner1.domainScores.find((d)=>d.domain === domain)) === null || _partner1_domainScores_find === void 0 ? void 0 : _partner1_domainScores_find.score) || 0;\n    const p2Score = ((_partner2_domainScores_find = partner2.domainScores.find((d)=>d.domain === domain)) === null || _partner2_domainScores_find === void 0 ? void 0 : _partner2_domainScores_find.score) || 0;\n    const status = getCompatibilityStatus(compatibilityScore);\n    const insights = generateDomainInsights(domain, partner1, partner2, compatibilityScore, partner1Name, partner2Name);\n    const recommendations = generateDomainRecommendations(domain, partner1, partner2, status);\n    return {\n        domain,\n        title: (domainInfo === null || domainInfo === void 0 ? void 0 : domainInfo.title) || domain,\n        description: (domainInfo === null || domainInfo === void 0 ? void 0 : domainInfo.description) || \"\",\n        partner1Score: p1Score,\n        partner2Score: p2Score,\n        compatibilityScore,\n        status,\n        insights,\n        recommendations\n    };\n}\n// Determine compatibility status\nfunction getCompatibilityStatus(score) {\n    if (score >= 80) return \"aligned\";\n    if (score >= 60) return \"moderate\";\n    return \"conflict\";\n}\n// Get compatibility level description\nfunction getCompatibilityLevel(score) {\n    if (score >= 90) return \"Sangat Tinggi\";\n    if (score >= 80) return \"Tinggi\";\n    if (score >= 60) return \"Sedang\";\n    if (score >= 40) return \"Rendah\";\n    return \"Sangat Rendah\";\n}\n// Generate insights for specific domains\nfunction generateDomainInsights(domain, partner1, partner2, compatibilityScore, partner1Name, partner2Name) {\n    const insights = [];\n    switch(domain){\n        case \"pengasuhan\":\n            const p1ParentingStyle = partner1.categories[\"parenting-style\"];\n            const p2ParentingStyle = partner2.categories[\"parenting-style\"];\n            if (p1ParentingStyle && p2ParentingStyle) {\n                if (p1ParentingStyle === p2ParentingStyle) {\n                    insights.push(\"Kedua pasangan memiliki gaya pengasuhan yang sama: \".concat(p1ParentingStyle));\n                } else {\n                    insights.push(\"Perbedaan gaya pengasuhan: \".concat(partner1Name || \"Partner 1\", \" (\").concat(p1ParentingStyle, \") vs \").concat(partner2Name || \"Partner 2\", \" (\").concat(p2ParentingStyle, \")\"));\n                }\n            }\n            break;\n        case \"komunikasi\":\n            const p1CommStyle = partner1.categories[\"communication-style\"];\n            const p2CommStyle = partner2.categories[\"communication-style\"];\n            if (p1CommStyle && p2CommStyle) {\n                insights.push(\"Gaya komunikasi: \".concat(partner1Name || \"Partner 1\", \" (\").concat(p1CommStyle, \") vs \").concat(partner2Name || \"Partner 2\", \" (\").concat(p2CommStyle, \")\"));\n                if (p1CommStyle === \"Asertif\" && p2CommStyle === \"Asertif\") {\n                    insights.push(\"Kedua pasangan memiliki gaya komunikasi yang ideal untuk hubungan yang sehat\");\n                }\n            }\n            break;\n        case \"fungsi-dan-peran\":\n            const p1MaleRole = partner1.categories[\"biblical-male-role\"];\n            const p2MaleRole = partner2.categories[\"biblical-male-role\"];\n            if (p1MaleRole && p2MaleRole) {\n                insights.push(\"Pandangan tentang peran pria: \".concat(partner1Name || \"Partner 1\", \" (\").concat(p1MaleRole, \") vs \").concat(partner2Name || \"Partner 2\", \" (\").concat(p2MaleRole, \")\"));\n            }\n            break;\n        case \"sisi-gelap\":\n            const p1DarkEmotion = partner1.categories[\"negative-emotion\"];\n            const p2DarkEmotion = partner2.categories[\"negative-emotion\"];\n            if (p1DarkEmotion && p1DarkEmotion !== \"Tidak ada\") {\n                insights.push(\"\".concat(partner1Name || \"Partner 1\", \" cenderung mengalami \").concat(p1DarkEmotion.toLowerCase()));\n            }\n            if (p2DarkEmotion && p2DarkEmotion !== \"Tidak ada\") {\n                insights.push(\"\".concat(partner2Name || \"Partner 2\", \" cenderung mengalami \").concat(p2DarkEmotion.toLowerCase()));\n            }\n            break;\n    }\n    // Add general compatibility insight\n    if (compatibilityScore >= 80) {\n        insights.push(\"Area ini menunjukkan keselarasan yang baik antara kedua pasangan\");\n    } else if (compatibilityScore <= 50) {\n        insights.push(\"Area ini memerlukan perhatian khusus dan diskusi mendalam\");\n    }\n    return insights;\n}\n// Generate domain-specific recommendations\nfunction generateDomainRecommendations(domain, partner1, partner2, status) {\n    const recommendations = [];\n    if (status === \"conflict\") {\n        switch(domain){\n            case \"komunikasi\":\n                recommendations.push(\"Ikuti pelatihan komunikasi untuk pasangan\");\n                recommendations.push(\"Praktikkan teknik mendengarkan aktif\");\n                recommendations.push(\"Tetapkan aturan untuk diskusi yang konstruktif\");\n                break;\n            case \"pengasuhan\":\n                recommendations.push(\"Diskusikan filosofi pengasuhan sebelum memiliki anak\");\n                recommendations.push(\"Baca buku tentang pengasuhan bersama-sama\");\n                recommendations.push(\"Konsultasi dengan ahli pengasuhan anak\");\n                break;\n            case \"keuangan\":\n                recommendations.push(\"Buat rencana keuangan bersama\");\n                recommendations.push(\"Diskusikan transparansi keuangan\");\n                recommendations.push(\"Konsultasi dengan perencana keuangan\");\n                break;\n            case \"spiritualitas\":\n                recommendations.push(\"Diskusikan harapan spiritual dalam pernikahan\");\n                recommendations.push(\"Cari mentor spiritual untuk pasangan\");\n                recommendations.push(\"Rencanakan aktivitas spiritual bersama\");\n                break;\n        }\n    } else if (status === \"aligned\") {\n        recommendations.push(\"Pertahankan keselarasan yang sudah baik di area ini\");\n        recommendations.push(\"Gunakan kekuatan ini untuk mendukung area lain yang memerlukan perbaikan\");\n    }\n    return recommendations;\n}\n// Generate priority recommendations for the couple\nfunction generatePriorityRecommendations(domainAnalyses, partner1, partner2) {\n    const recommendations = [];\n    // Focus on conflict areas first\n    const conflictAreas = domainAnalyses.filter((d)=>d.status === \"conflict\");\n    if (conflictAreas.length > 0) {\n        recommendations.push(\"Prioritaskan diskusi mendalam tentang area-area konflik yang teridentifikasi\");\n        // Specific high-priority recommendations\n        if (conflictAreas.some((d)=>d.domain === \"komunikasi\")) {\n            recommendations.push(\"PRIORITAS TINGGI: Perbaiki pola komunikasi sebelum menikah\");\n        }\n        if (conflictAreas.some((d)=>d.domain === \"fungsi-dan-peran\")) {\n            recommendations.push(\"PRIORITAS TINGGI: Klarifikasi ekspektasi peran dalam pernikahan\");\n        }\n    }\n    // Add general recommendations\n    recommendations.push(\"Lakukan sesi konseling pra-nikah dengan konselor yang berpengalaman\");\n    recommendations.push(\"Buat rencana konkret untuk mengatasi area-area yang memerlukan perbaikan\");\n    return recommendations;\n}\n// Generate notes for counselors\nfunction generateCounselorNotes(domainAnalyses, partner1, partner2) {\n    const notes = [];\n    // Overall assessment\n    const conflictCount = domainAnalyses.filter((d)=>d.status === \"conflict\").length;\n    const alignedCount = domainAnalyses.filter((d)=>d.status === \"aligned\").length;\n    notes.push(\"Jumlah area konflik: \".concat(conflictCount, \"/8\"));\n    notes.push(\"Jumlah area selaras: \".concat(alignedCount, \"/8\"));\n    // Specific counselor guidance\n    if (conflictCount >= 4) {\n        notes.push(\"PERHATIAN: Banyak area konflik terdeteksi. Pertimbangkan sesi konseling intensif.\");\n    }\n    // Check for critical combinations\n    const criticalDomains = [\n        \"komunikasi\",\n        \"fungsi-dan-peran\",\n        \"spiritualitas\"\n    ];\n    const criticalConflicts = domainAnalyses.filter((d)=>criticalDomains.includes(d.domain) && d.status === \"conflict\");\n    if (criticalConflicts.length >= 2) {\n        notes.push(\"PERINGATAN: Konflik di area-area fundamental pernikahan terdeteksi.\");\n    }\n    return notes;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts\n"));

/***/ })

});