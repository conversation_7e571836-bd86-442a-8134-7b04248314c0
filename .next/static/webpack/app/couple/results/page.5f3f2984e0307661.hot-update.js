"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/couple/results/page",{

/***/ "(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts":
/*!**********************************************!*\
  !*** ./src/lib/assessment/resultAnalysis.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCoupleAnalysisReport: function() { return /* binding */ generateCoupleAnalysisReport; }\n/* harmony export */ });\n// Domain titles and descriptions in Indonesian\nconst DOMAIN_INFO = {\n    \"visi-hidup\": {\n        title: \"Visi Hidup\",\n        description: \"Keselarasan tujuan dan aspirasi jangka panjang dalam pernikahan\"\n    },\n    keuangan: {\n        title: \"Keuangan\",\n        description: \"Pendekatan terhadap pengelolaan keuangan dan transparansi finansial\"\n    },\n    pengasuhan: {\n        title: \"Pengasuhan Anak\",\n        description: \"Gaya dan filosofi dalam mendidik dan membesarkan anak\"\n    },\n    komunikasi: {\n        title: \"Komunikasi\",\n        description: \"Cara berkomunikasi dan menyelesaikan konflik dalam hubungan\"\n    },\n    \"fungsi-dan-peran\": {\n        title: \"Fungsi dan Peran\",\n        description: \"Pemahaman tentang peran suami-istri berdasarkan nilai-nilai Alkitab\"\n    },\n    seks: {\n        title: \"Keintiman Seksual\",\n        description: \"Pandangan dan ekspektasi tentang keintiman dalam pernikahan\"\n    },\n    spiritualitas: {\n        title: \"Spiritualitas\",\n        description: \"Keselarasan dalam pertumbuhan iman dan praktik spiritual bersama\"\n    },\n    \"sisi-gelap\": {\n        title: \"Sisi Gelap\",\n        description: \"Pengelolaan emosi negatif dan potensi masalah dalam hubungan\"\n    }\n};\n// Generate comprehensive analysis report\nfunction generateCoupleAnalysisReport(compatibilityResult, partner1Name1, partner2Name1) {\n    const { partner1, partner2, compatibilityScores, overallCompatibility } = compatibilityResult;\n    // Determine compatibility level\n    const compatibilityLevel = getCompatibilityLevel(overallCompatibility);\n    // Generate domain analyses\n    const domainAnalyses = [];\n    Object.entries(compatibilityScores).forEach((param)=>{\n        let [domain, score] = param;\n        const analysis = generateDomainAnalysis(domain, partner1, partner2, score);\n        domainAnalyses.push(analysis);\n    });\n    // Identify strength and challenge areas\n    const strengthAreas = domainAnalyses.filter((d)=>d.status === \"aligned\").map((d)=>d.title);\n    const challengeAreas = domainAnalyses.filter((d)=>d.status === \"conflict\").map((d)=>d.title);\n    // Generate priority recommendations\n    const priorityRecommendations = generatePriorityRecommendations(domainAnalyses, partner1, partner2);\n    // Generate counselor notes\n    const counselorNotes = generateCounselorNotes(domainAnalyses, partner1, partner2);\n    return {\n        overallCompatibility,\n        compatibilityLevel,\n        domainAnalyses,\n        strengthAreas,\n        challengeAreas,\n        priorityRecommendations,\n        counselorNotes,\n        partner1Name: partner1Name1,\n        partner2Name: partner2Name1\n    };\n}\n// Generate analysis for a specific domain\nfunction generateDomainAnalysis(domain, partner1, partner2, compatibilityScore) {\n    var _partner1_domainScores_find, _partner2_domainScores_find;\n    const domainInfo = DOMAIN_INFO[domain];\n    const p1Score = ((_partner1_domainScores_find = partner1.domainScores.find((d)=>d.domain === domain)) === null || _partner1_domainScores_find === void 0 ? void 0 : _partner1_domainScores_find.score) || 0;\n    const p2Score = ((_partner2_domainScores_find = partner2.domainScores.find((d)=>d.domain === domain)) === null || _partner2_domainScores_find === void 0 ? void 0 : _partner2_domainScores_find.score) || 0;\n    const status = getCompatibilityStatus(compatibilityScore);\n    const insights = generateDomainInsights(domain, partner1, partner2, compatibilityScore, partner1Name, partner2Name);\n    const recommendations = generateDomainRecommendations(domain, partner1, partner2, status);\n    return {\n        domain,\n        title: (domainInfo === null || domainInfo === void 0 ? void 0 : domainInfo.title) || domain,\n        description: (domainInfo === null || domainInfo === void 0 ? void 0 : domainInfo.description) || \"\",\n        partner1Score: p1Score,\n        partner2Score: p2Score,\n        compatibilityScore,\n        status,\n        insights,\n        recommendations\n    };\n}\n// Determine compatibility status\nfunction getCompatibilityStatus(score) {\n    if (score >= 80) return \"aligned\";\n    if (score >= 60) return \"moderate\";\n    return \"conflict\";\n}\n// Get compatibility level description\nfunction getCompatibilityLevel(score) {\n    if (score >= 90) return \"Sangat Tinggi\";\n    if (score >= 80) return \"Tinggi\";\n    if (score >= 60) return \"Sedang\";\n    if (score >= 40) return \"Rendah\";\n    return \"Sangat Rendah\";\n}\n// Generate insights for specific domains\nfunction generateDomainInsights(domain, partner1, partner2, compatibilityScore, partner1Name1, partner2Name1) {\n    const insights = [];\n    switch(domain){\n        case \"pengasuhan\":\n            const p1ParentingStyle = partner1.categories[\"parenting-style\"];\n            const p2ParentingStyle = partner2.categories[\"parenting-style\"];\n            if (p1ParentingStyle && p2ParentingStyle) {\n                if (p1ParentingStyle === p2ParentingStyle) {\n                    insights.push(\"Kedua pasangan memiliki gaya pengasuhan yang sama: \".concat(p1ParentingStyle));\n                } else {\n                    insights.push(\"Perbedaan gaya pengasuhan: \".concat(partner1Name1 || \"Partner 1\", \" (\").concat(p1ParentingStyle, \") vs \").concat(partner2Name1 || \"Partner 2\", \" (\").concat(p2ParentingStyle, \")\"));\n                }\n            }\n            break;\n        case \"komunikasi\":\n            const p1CommStyle = partner1.categories[\"communication-style\"];\n            const p2CommStyle = partner2.categories[\"communication-style\"];\n            if (p1CommStyle && p2CommStyle) {\n                insights.push(\"Gaya komunikasi: \".concat(partner1Name1 || \"Partner 1\", \" (\").concat(p1CommStyle, \") vs \").concat(partner2Name1 || \"Partner 2\", \" (\").concat(p2CommStyle, \")\"));\n                if (p1CommStyle === \"Asertif\" && p2CommStyle === \"Asertif\") {\n                    insights.push(\"Kedua pasangan memiliki gaya komunikasi yang ideal untuk hubungan yang sehat\");\n                }\n            }\n            break;\n        case \"fungsi-dan-peran\":\n            const p1MaleRole = partner1.categories[\"biblical-male-role\"];\n            const p2MaleRole = partner2.categories[\"biblical-male-role\"];\n            if (p1MaleRole && p2MaleRole) {\n                insights.push(\"Pandangan tentang peran pria: \".concat(partner1Name1 || \"Partner 1\", \" (\").concat(p1MaleRole, \") vs \").concat(partner2Name1 || \"Partner 2\", \" (\").concat(p2MaleRole, \")\"));\n            }\n            break;\n        case \"sisi-gelap\":\n            const p1DarkEmotion = partner1.categories[\"negative-emotion\"];\n            const p2DarkEmotion = partner2.categories[\"negative-emotion\"];\n            if (p1DarkEmotion && p1DarkEmotion !== \"Tidak ada\") {\n                insights.push(\"\".concat(partner1Name1 || \"Partner 1\", \" cenderung mengalami \").concat(p1DarkEmotion.toLowerCase()));\n            }\n            if (p2DarkEmotion && p2DarkEmotion !== \"Tidak ada\") {\n                insights.push(\"\".concat(partner2Name1 || \"Partner 2\", \" cenderung mengalami \").concat(p2DarkEmotion.toLowerCase()));\n            }\n            break;\n    }\n    // Add general compatibility insight\n    if (compatibilityScore >= 80) {\n        insights.push(\"Area ini menunjukkan keselarasan yang baik antara kedua pasangan\");\n    } else if (compatibilityScore <= 50) {\n        insights.push(\"Area ini memerlukan perhatian khusus dan diskusi mendalam\");\n    }\n    return insights;\n}\n// Generate domain-specific recommendations\nfunction generateDomainRecommendations(domain, partner1, partner2, status) {\n    const recommendations = [];\n    if (status === \"conflict\") {\n        switch(domain){\n            case \"komunikasi\":\n                recommendations.push(\"Ikuti pelatihan komunikasi untuk pasangan\");\n                recommendations.push(\"Praktikkan teknik mendengarkan aktif\");\n                recommendations.push(\"Tetapkan aturan untuk diskusi yang konstruktif\");\n                break;\n            case \"pengasuhan\":\n                recommendations.push(\"Diskusikan filosofi pengasuhan sebelum memiliki anak\");\n                recommendations.push(\"Baca buku tentang pengasuhan bersama-sama\");\n                recommendations.push(\"Konsultasi dengan ahli pengasuhan anak\");\n                break;\n            case \"keuangan\":\n                recommendations.push(\"Buat rencana keuangan bersama\");\n                recommendations.push(\"Diskusikan transparansi keuangan\");\n                recommendations.push(\"Konsultasi dengan perencana keuangan\");\n                break;\n            case \"spiritualitas\":\n                recommendations.push(\"Diskusikan harapan spiritual dalam pernikahan\");\n                recommendations.push(\"Cari mentor spiritual untuk pasangan\");\n                recommendations.push(\"Rencanakan aktivitas spiritual bersama\");\n                break;\n        }\n    } else if (status === \"aligned\") {\n        recommendations.push(\"Pertahankan keselarasan yang sudah baik di area ini\");\n        recommendations.push(\"Gunakan kekuatan ini untuk mendukung area lain yang memerlukan perbaikan\");\n    }\n    return recommendations;\n}\n// Generate priority recommendations for the couple\nfunction generatePriorityRecommendations(domainAnalyses, partner1, partner2) {\n    const recommendations = [];\n    // Focus on conflict areas first\n    const conflictAreas = domainAnalyses.filter((d)=>d.status === \"conflict\");\n    if (conflictAreas.length > 0) {\n        recommendations.push(\"Prioritaskan diskusi mendalam tentang area-area konflik yang teridentifikasi\");\n        // Specific high-priority recommendations\n        if (conflictAreas.some((d)=>d.domain === \"komunikasi\")) {\n            recommendations.push(\"PRIORITAS TINGGI: Perbaiki pola komunikasi sebelum menikah\");\n        }\n        if (conflictAreas.some((d)=>d.domain === \"fungsi-dan-peran\")) {\n            recommendations.push(\"PRIORITAS TINGGI: Klarifikasi ekspektasi peran dalam pernikahan\");\n        }\n    }\n    // Add general recommendations\n    recommendations.push(\"Lakukan sesi konseling pra-nikah dengan konselor yang berpengalaman\");\n    recommendations.push(\"Buat rencana konkret untuk mengatasi area-area yang memerlukan perbaikan\");\n    return recommendations;\n}\n// Generate notes for counselors\nfunction generateCounselorNotes(domainAnalyses, partner1, partner2) {\n    const notes = [];\n    // Overall assessment\n    const conflictCount = domainAnalyses.filter((d)=>d.status === \"conflict\").length;\n    const alignedCount = domainAnalyses.filter((d)=>d.status === \"aligned\").length;\n    notes.push(\"Jumlah area konflik: \".concat(conflictCount, \"/8\"));\n    notes.push(\"Jumlah area selaras: \".concat(alignedCount, \"/8\"));\n    // Specific counselor guidance\n    if (conflictCount >= 4) {\n        notes.push(\"PERHATIAN: Banyak area konflik terdeteksi. Pertimbangkan sesi konseling intensif.\");\n    }\n    // Check for critical combinations\n    const criticalDomains = [\n        \"komunikasi\",\n        \"fungsi-dan-peran\",\n        \"spiritualitas\"\n    ];\n    const criticalConflicts = domainAnalyses.filter((d)=>criticalDomains.includes(d.domain) && d.status === \"conflict\");\n    if (criticalConflicts.length >= 2) {\n        notes.push(\"PERINGATAN: Konflik di area-area fundamental pernikahan terdeteksi.\");\n    }\n    return notes;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/assessment/resultAnalysis.ts\n"));

/***/ })

});