"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/debug/page",{

/***/ "(app-pages-browser)/./src/app/debug/page.tsx":
/*!********************************!*\
  !*** ./src/app/debug/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DebugPage() {\n    var _data_couples, _data_profiles, _data_individualResults, _data_coupleResults, _data_codes;\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchData() {\n            const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n            try {\n                // Get current user\n                const { data: { user } } = await supabase.auth.getUser();\n                console.log(\"Current user:\", user);\n                // Get all couples\n                const { data: couples, error: couplesError } = await supabase.from(\"couples\").select(\"*\").order(\"created_at\", {\n                    ascending: false\n                });\n                console.log(\"Couples:\", couples);\n                console.log(\"Couples error:\", couplesError);\n                // Get all profiles\n                const { data: profiles, error: profilesError } = await supabase.from(\"profiles\").select(\"*\").order(\"created_at\", {\n                    ascending: false\n                });\n                console.log(\"Profiles:\", profiles);\n                console.log(\"Profiles error:\", profilesError);\n                // Get all individual results\n                const { data: individualResults, error: individualError } = await supabase.from(\"individual_results\").select(\"*\").order(\"updated_at\", {\n                    ascending: false\n                });\n                console.log(\"Individual results:\", individualResults);\n                console.log(\"Individual results error:\", individualError);\n                // Get all couple results\n                const { data: coupleResults, error: coupleResultsError } = await supabase.from(\"couple_results\").select(\"*\").order(\"created_at\", {\n                    ascending: false\n                });\n                console.log(\"Couple results:\", coupleResults);\n                console.log(\"Couple results error:\", coupleResultsError);\n                // Get all invitation codes\n                const { data: codes, error: codesError } = await supabase.from(\"couple_invitation_codes\").select(\"*\").order(\"created_at\", {\n                    ascending: false\n                });\n                console.log(\"Invitation codes:\", codes);\n                console.log(\"Invitation codes error:\", codesError);\n                setData({\n                    user,\n                    couples,\n                    profiles,\n                    individualResults,\n                    coupleResults,\n                    codes,\n                    errors: {\n                        couplesError,\n                        profilesError,\n                        individualError,\n                        coupleResultsError,\n                        codesError\n                    }\n                });\n            } catch (error) {\n                console.error(\"Debug fetch error:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n        fetchData();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8\",\n            children: \"Loading debug data...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n            lineNumber: 89,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 max-w-6xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold mb-6\",\n                children: \"Debug Data\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: \"Current User\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.user, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: [\n                                    \"Couples (\",\n                                    ((_data_couples = data.couples) === null || _data_couples === void 0 ? void 0 : _data_couples.length) || 0,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.couples, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: [\n                                    \"Profiles (\",\n                                    ((_data_profiles = data.profiles) === null || _data_profiles === void 0 ? void 0 : _data_profiles.length) || 0,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.profiles, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: [\n                                    \"Individual Results (\",\n                                    ((_data_individualResults = data.individualResults) === null || _data_individualResults === void 0 ? void 0 : _data_individualResults.length) || 0,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.individualResults, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: [\n                                    \"Couple Results (\",\n                                    ((_data_coupleResults = data.coupleResults) === null || _data_coupleResults === void 0 ? void 0 : _data_coupleResults.length) || 0,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.coupleResults, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: [\n                                    \"Invitation Codes (\",\n                                    ((_data_codes = data.codes) === null || _data_codes === void 0 ? void 0 : _data_codes.length) || 0,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.codes, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 p-4 rounded\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold mb-2\",\n                                children: \"Errors\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(data.errors, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Nextjs/marriage-map/src/app/debug/page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugPage, \"+2Aek85bYeV0JwBHRmtAxqiaVyE=\");\n_c = DebugPage;\nvar _c;\n$RefreshReg$(_c, \"DebugPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/debug/page.tsx\n"));

/***/ })

});