-- Test script to verify couple results access
-- Run this in Supabase SQL Editor after applying fixes

-- 1. Check if we have test data
SELECT 'Checking test data availability...' as status;

-- Check couples
SELECT 
  couple_id,
  user_id_1,
  user_id_2,
  created_at
FROM couples
ORDER BY created_at DESC
LIMIT 5;

-- Check profiles
SELECT 
  id,
  full_name,
  email,
  created_at
FROM profiles
ORDER BY created_at DESC
LIMIT 5;

-- Check individual results
SELECT 
  user_id,
  COUNT(*) as domain_count,
  MAX(updated_at) as last_updated
FROM individual_results
GROUP BY user_id
ORDER BY last_updated DESC;

-- 2. Test couple results access for each user
-- Replace these UUIDs with actual user IDs from your data
DO $$
DECLARE
    test_user_id UUID;
    couple_record RECORD;
BEGIN
    -- Get a test user who is in a couple
    SELECT user_id_1 INTO test_user_id
    FROM couples
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE 'Testing access for user: %', test_user_id;
        
        -- Test couple access
        SELECT couple_id, user_id_1, user_id_2 INTO couple_record
        FROM couples
        WHERE user_id_1 = test_user_id OR user_id_2 = test_user_id;
        
        IF FOUND THEN
            RAISE NOTICE 'User can access couple: %', couple_record.couple_id;
        ELSE
            RAISE NOTICE 'User cannot access couple data';
        END IF;
    ELSE
        RAISE NOTICE 'No test couples found';
    END IF;
END $$;

-- 3. Test couple results table structure
SELECT 'Testing couple_results table structure...' as status;

-- Check if table exists and has correct columns
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'couple_results'
ORDER BY ordinal_position;

-- 4. Test RLS policies
SELECT 'Testing RLS policies...' as status;

-- Check if RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE tablename IN ('couples', 'profiles', 'couple_results', 'individual_results');

-- 5. Simulate couple results creation (manual test)
SELECT 'Manual test data for couple results...' as status;

-- This shows what data would be inserted
SELECT 
    c.couple_id,
    'Test compatibility data' as compatibility_scores,
    85 as overall_compatibility,
    '["Communication", "Shared Values"]' as alignment_areas,
    '["Financial Planning"]' as conflict_areas,
    NOW() as created_at
FROM couples c
LIMIT 1;
