-- Check foreign key constraints for couples and profiles tables
-- Run this in Supabase SQL Editor

-- 1. Check all foreign key constraints for couples table
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name = 'couples';

-- 2. Check all foreign key constraints for profiles table
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name = 'profiles';

-- 3. Test the JOIN query manually to see if it works
SELECT 
  c.couple_id,
  c.user_id_1,
  c.user_id_2,
  p1.full_name as user1_name,
  p1.email as user1_email,
  p2.full_name as user2_name,
  p2.email as user2_email
FROM couples c
LEFT JOIN profiles p1 ON c.user_id_1 = p1.id
LEFT JOIN profiles p2 ON c.user_id_2 = p2.id
ORDER BY c.created_at DESC
LIMIT 5;

-- 4. Check if there are any couples and profiles data
SELECT COUNT(*) as couples_count FROM couples;
SELECT COUNT(*) as profiles_count FROM profiles;

-- 5. Check individual_results data
SELECT 
  user_id,
  COUNT(*) as results_count,
  MAX(updated_at) as last_updated
FROM individual_results 
GROUP BY user_id
ORDER BY last_updated DESC;
